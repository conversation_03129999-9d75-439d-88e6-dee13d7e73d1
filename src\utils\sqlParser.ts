/**
 * SQL解析工具（基础版本）
 * 用于解析SQL语句并提取血缘关系
 */

import type { LineageData, LineageNode, LineageEdge, TableInfo } from '@/types/lineage'
import { generateUniqueId, generateFieldId } from './graphDataTransform'

/**
 * SQL格式化选项
 */
export interface SqlFormatOptions {
  indent: string;
  uppercase: boolean;
  linesBetweenQueries: number;
}

/**
 * 默认格式化选项
 */
const DEFAULT_FORMAT_OPTIONS: SqlFormatOptions = {
  indent: '  ',
  uppercase: true,
  linesBetweenQueries: 2
}

/**
 * 格式化SQL语句
 * @param sql 原始SQL
 * @param options 格式化选项
 * @returns 格式化后的SQL
 */
export function formatSql(sql: string, options: Partial<SqlFormatOptions> = {}): string {
  const opts = { ...DEFAULT_FORMAT_OPTIONS, ...options }
  
  // 简单的SQL格式化实现
  let formatted = sql
    .replace(/\s+/g, ' ') // 合并多个空格
    .trim()
  
  // 关键字大写
  if (opts.uppercase) {
    const keywords = [
      'SELECT', 'FROM', 'WHERE', 'JOIN', 'INNER JOIN', 'LEFT JOIN', 'RIGHT JOIN',
      'FULL JOIN', 'ON', 'GROUP BY', 'ORDER BY', 'HAVING', 'UNION', 'UNION ALL',
      'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'TABLE', 'VIEW',
      'INDEX', 'DATABASE', 'SCHEMA', 'AS', 'AND', 'OR', 'NOT', 'IN', 'EXISTS',
      'BETWEEN', 'LIKE', 'IS', 'NULL', 'DISTINCT', 'COUNT', 'SUM', 'AVG', 'MAX', 'MIN'
    ]
    
    keywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'gi')
      formatted = formatted.replace(regex, keyword)
    })
  }
  
  // 添加换行和缩进
  formatted = formatted
    .replace(/\bSELECT\b/gi, '\nSELECT')
    .replace(/\bFROM\b/gi, '\nFROM')
    .replace(/\bWHERE\b/gi, '\nWHERE')
    .replace(/\bJOIN\b/gi, '\nJOIN')
    .replace(/\bINNER JOIN\b/gi, '\nINNER JOIN')
    .replace(/\bLEFT JOIN\b/gi, '\nLEFT JOIN')
    .replace(/\bRIGHT JOIN\b/gi, '\nRIGHT JOIN')
    .replace(/\bFULL JOIN\b/gi, '\nFULL JOIN')
    .replace(/\bGROUP BY\b/gi, '\nGROUP BY')
    .replace(/\bORDER BY\b/gi, '\nORDER BY')
    .replace(/\bHAVING\b/gi, '\nHAVING')
    .replace(/\bUNION\b/gi, '\nUNION')
  
  // 添加缩进
  const lines = formatted.split('\n')
  const indentedLines = lines.map((line, index) => {
    if (index === 0) return line.trim()
    return opts.indent + line.trim()
  })
  
  return indentedLines.join('\n').trim()
}

/**
 * 解析SQL中的表名（简单实现）
 * @param sql SQL语句
 * @returns 表名数组
 */
export function extractTableNames(sql: string): string[] {
  const tables: string[] = []
  
  // 简单的正则表达式匹配表名
  const fromRegex = /FROM\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)/gi
  const joinRegex = /JOIN\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)/gi
  
  let match
  
  // 提取FROM子句中的表名
  while ((match = fromRegex.exec(sql)) !== null) {
    const tableName = match[1].trim()
    if (!tables.includes(tableName)) {
      tables.push(tableName)
    }
  }
  
  // 提取JOIN子句中的表名
  while ((match = joinRegex.exec(sql)) !== null) {
    const tableName = match[1].trim()
    if (!tables.includes(tableName)) {
      tables.push(tableName)
    }
  }
  
  return tables
}

/**
 * 解析SQL中的字段名（简单实现）
 * @param sql SQL语句
 * @returns 字段名数组
 */
export function extractFieldNames(sql: string): string[] {
  const fields: string[] = []
  
  // 提取SELECT子句中的字段
  const selectRegex = /SELECT\s+(.*?)\s+FROM/gis
  const match = selectRegex.exec(sql)
  
  if (match) {
    const selectClause = match[1]
    
    // 分割字段（简单处理，不考虑复杂的嵌套情况）
    const fieldParts = selectClause.split(',')
    
    fieldParts.forEach(part => {
      const trimmed = part.trim()
      
      // 跳过聚合函数和复杂表达式
      if (trimmed === '*' || trimmed.includes('(')) {
        return
      }
      
      // 提取字段名（可能包含表名前缀）
      const fieldMatch = trimmed.match(/([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)?)/)
      if (fieldMatch) {
        fields.push(fieldMatch[1])
      }
    })
  }
  
  return fields
}

/**
 * 创建示例血缘数据（用于演示）
 * @param sql SQL语句
 * @returns 血缘数据
 */
export function createSampleLineageData(sql?: string): LineageData {
  // 创建示例表数据
  const tables: { [key: string]: TableInfo } = {
    'users': {
      name: 'users',
      type: 'table',
      description: '用户表',
      fields: [
        {
          id: 'users.id',
          label: 'id',
          tableName: 'users',
          fieldName: 'id',
          type: 'field',
          dataType: { type: 'INT' },
          description: '用户ID',
          isKey: true,
          isNullable: false
        },
        {
          id: 'users.name',
          label: 'name',
          tableName: 'users',
          fieldName: 'name',
          type: 'field',
          dataType: { type: 'VARCHAR', length: 100 },
          description: '用户姓名',
          isNullable: false
        },
        {
          id: 'users.email',
          label: 'email',
          tableName: 'users',
          fieldName: 'email',
          type: 'field',
          dataType: { type: 'VARCHAR', length: 255 },
          description: '邮箱地址',
          isNullable: true
        },
        {
          id: 'users.created_at',
          label: 'created_at',
          tableName: 'users',
          fieldName: 'created_at',
          type: 'field',
          dataType: { type: 'TIMESTAMP' },
          description: '创建时间',
          isNullable: false
        }
      ],
      position: { x: 100, y: 100 }
    },
    'orders': {
      name: 'orders',
      type: 'table',
      description: '订单表',
      fields: [
        {
          id: 'orders.id',
          label: 'id',
          tableName: 'orders',
          fieldName: 'id',
          type: 'field',
          dataType: { type: 'INT' },
          description: '订单ID',
          isKey: true,
          isNullable: false
        },
        {
          id: 'orders.user_id',
          label: 'user_id',
          tableName: 'orders',
          fieldName: 'user_id',
          type: 'field',
          dataType: { type: 'INT' },
          description: '用户ID',
          isNullable: false
        },
        {
          id: 'orders.amount',
          label: 'amount',
          tableName: 'orders',
          fieldName: 'amount',
          type: 'field',
          dataType: { type: 'DECIMAL', precision: 10, scale: 2 },
          description: '订单金额',
          isNullable: false
        },
        {
          id: 'orders.created_at',
          label: 'created_at',
          tableName: 'orders',
          fieldName: 'created_at',
          type: 'field',
          dataType: { type: 'TIMESTAMP' },
          description: '创建时间',
          isNullable: false
        }
      ],
      position: { x: 400, y: 100 }
    },
    'user_order_summary': {
      name: 'user_order_summary',
      type: 'view',
      description: '用户订单汇总视图',
      fields: [
        {
          id: 'user_order_summary.user_id',
          label: 'user_id',
          tableName: 'user_order_summary',
          fieldName: 'user_id',
          type: 'field',
          dataType: { type: 'INT' },
          description: '用户ID',
          isNullable: false
        },
        {
          id: 'user_order_summary.user_name',
          label: 'user_name',
          tableName: 'user_order_summary',
          fieldName: 'user_name',
          type: 'field',
          dataType: { type: 'VARCHAR', length: 100 },
          description: '用户姓名',
          isNullable: false
        },
        {
          id: 'user_order_summary.total_amount',
          label: 'total_amount',
          tableName: 'user_order_summary',
          fieldName: 'total_amount',
          type: 'field',
          dataType: { type: 'DECIMAL', precision: 12, scale: 2 },
          description: '总订单金额',
          isNullable: true
        },
        {
          id: 'user_order_summary.order_count',
          label: 'order_count',
          tableName: 'user_order_summary',
          fieldName: 'order_count',
          type: 'field',
          dataType: { type: 'INT' },
          description: '订单数量',
          isNullable: true
        }
      ],
      position: { x: 700, y: 100 }
    }
  }

  // 创建所有节点
  const nodes: LineageNode[] = []
  Object.values(tables).forEach(table => {
    nodes.push(...table.fields)
  })

  // 创建示例边数据
  const edges: LineageEdge[] = [
    {
      id: generateUniqueId('edge'),
      source: 'users.id',
      target: 'user_order_summary.user_id',
      label: 'Direct',
      transformType: 'DIRECT',
      confidence: 1.0
    },
    {
      id: generateUniqueId('edge'),
      source: 'users.name',
      target: 'user_order_summary.user_name',
      label: 'Direct',
      transformType: 'DIRECT',
      confidence: 1.0
    },
    {
      id: generateUniqueId('edge'),
      source: 'orders.user_id',
      target: 'user_order_summary.user_id',
      label: 'Join',
      transformType: 'JOIN',
      confidence: 0.9
    },
    {
      id: generateUniqueId('edge'),
      source: 'orders.amount',
      target: 'user_order_summary.total_amount',
      label: 'Aggregate',
      transformType: 'AGGREGATE',
      expression: 'SUM(orders.amount)',
      confidence: 1.0
    },
    {
      id: generateUniqueId('edge'),
      source: 'orders.id',
      target: 'user_order_summary.order_count',
      label: 'Aggregate',
      transformType: 'AGGREGATE',
      expression: 'COUNT(orders.id)',
      confidence: 1.0
    }
  ]

  return {
    nodes,
    edges,
    tables,
    metadata: {
      sqlText: sql || 'SELECT u.id as user_id, u.name as user_name, SUM(o.amount) as total_amount, COUNT(o.id) as order_count FROM users u LEFT JOIN orders o ON u.id = o.user_id GROUP BY u.id, u.name',
      parseTime: new Date().toISOString(),
      version: '1.0.0'
    }
  }
}
