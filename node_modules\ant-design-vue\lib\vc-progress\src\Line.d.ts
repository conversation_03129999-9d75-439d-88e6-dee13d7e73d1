declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    gapDegree: NumberConstructor;
    gapPosition: {
        type: import("vue").PropType<import("./types").GapPositionType>;
    };
    percent: {
        type: import("vue").PropType<number | number[]>;
    };
    prefixCls: StringConstructor;
    strokeColor: {
        type: import("vue").PropType<import("./types").StrokeColorType>;
    };
    strokeLinecap: {
        type: import("vue").PropType<import("./types").StrokeLinecapType>;
    };
    strokeWidth: NumberConstructor;
    trailColor: StringConstructor;
    trailWidth: NumberConstructor;
    transition: StringConstructor;
}>, () => import("vue/jsx-runtime").JSX.Element, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    gapDegree: NumberConstructor;
    gapPosition: {
        type: import("vue").PropType<import("./types").GapPositionType>;
    };
    percent: {
        type: import("vue").PropType<number | number[]>;
    };
    prefixCls: StringConstructor;
    strokeColor: {
        type: import("vue").PropType<import("./types").StrokeColorType>;
    };
    strokeLinecap: {
        type: import("vue").PropType<import("./types").StrokeLinecapType>;
    };
    strokeWidth: NumberConstructor;
    trailColor: StringConstructor;
    trailWidth: NumberConstructor;
    transition: StringConstructor;
}>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
