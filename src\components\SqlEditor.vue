<template>
  <div class="sql-editor-container">
    <div class="editor-header">
      <div class="header-left">
        <label class="editor-label">SQL 语句</label>
        <a-tag v-if="lineCount > 0" color="blue" size="small">
          {{ lineCount }} 行
        </a-tag>
      </div>
      <div class="header-right">
        <a-space size="small">
          <a-button size="small" type="text" @click="handleFormat" :loading="formatting">
            <template #icon><FormatPainterOutlined /></template>
            格式化
          </a-button>
          <a-button size="small" type="text" @click="handleClear">
            <template #icon><ClearOutlined /></template>
            清空
          </a-button>
          <a-button size="small" type="text" @click="handleCopy" v-if="modelValue">
            <template #icon><CopyOutlined /></template>
            复制
          </a-button>
        </a-space>
      </div>
    </div>
    <div class="editor-wrapper" ref="editorContainer">
      <!-- CodeMirror 编辑器将在这里挂载 -->
    </div>
    <div class="editor-footer" v-if="showFooter">
      <div class="footer-left">
        <a-space size="small">
          <span class="status-text">
            <DatabaseOutlined />
            {{ databaseType.toUpperCase() }}
          </span>
          <span class="status-text" v-if="characterCount > 0">
            字符数: {{ characterCount }}
          </span>
        </a-space>
      </div>
      <div class="footer-right">
        <a-space size="small">
          <span class="cursor-position" v-if="cursorPosition">
            行 {{ cursorPosition.line }}, 列 {{ cursorPosition.column }}
          </span>
        </a-space>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
  FormatPainterOutlined,
  ClearOutlined,
  CopyOutlined,
  DatabaseOutlined
} from '@ant-design/icons-vue'

// CodeMirror imports
import { EditorView, basicSetup } from 'codemirror'
import { EditorState } from '@codemirror/state'
import { sql } from '@codemirror/lang-sql'
import { oneDark } from '@codemirror/theme-one-dark'

// Props
interface Props {
  modelValue?: string
  placeholder?: string
  databaseType?: string
  theme?: 'light' | 'dark'
  readonly?: boolean
  showFooter?: boolean
  height?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入 SQL 语句...',
  databaseType: 'mysql',
  theme: 'light',
  readonly: false,
  showFooter: true,
  height: '100%'
})

// Emits
interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
  (e: 'format', value: string): void
  (e: 'clear'): void
}

const emit = defineEmits<Emits>()

// Refs
const editorContainer = ref<HTMLElement>()
const formatting = ref(false)
const cursorPosition = ref<{ line: number; column: number } | null>(null)

// CodeMirror editor instance
let editorView: EditorView | null = null

// Computed
const lineCount = computed(() => {
  return props.modelValue ? props.modelValue.split('\n').length : 0
})

const characterCount = computed(() => {
  return props.modelValue ? props.modelValue.length : 0
})

// Methods
const initEditor = async () => {
  if (!editorContainer.value) return

  const extensions = [
    basicSetup,
    sql(),
    EditorView.updateListener.of((update) => {
      if (update.docChanged) {
        const newValue = update.state.doc.toString()
        emit('update:modelValue', newValue)
        emit('change', newValue)
      }
      
      // 更新光标位置
      if (update.selectionSet) {
        const cursor = update.state.selection.main.head
        const line = update.state.doc.lineAt(cursor)
        cursorPosition.value = {
          line: line.number,
          column: cursor - line.from + 1
        }
      }
    }),
    EditorView.theme({
      '&': {
        height: typeof props.height === 'number' ? `${props.height}px` : props.height,
        fontSize: '14px',
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace'
      },
      '.cm-content': {
        padding: '12px',
        minHeight: '200px'
      },
      '.cm-focused': {
        outline: 'none'
      },
      '.cm-editor': {
        borderRadius: '6px',
        border: '1px solid #d9d9d9'
      },
      '.cm-editor.cm-focused': {
        borderColor: '#1890ff',
        boxShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)'
      }
    })
  ]

  // 添加主题
  if (props.theme === 'dark') {
    extensions.push(oneDark)
  }

  // 只读模式
  if (props.readonly) {
    extensions.push(EditorState.readOnly.of(true))
  }

  const state = EditorState.create({
    doc: props.modelValue,
    extensions
  })

  editorView = new EditorView({
    state,
    parent: editorContainer.value
  })
}

const destroyEditor = () => {
  if (editorView) {
    editorView.destroy()
    editorView = null
  }
}

const updateEditorContent = (content: string) => {
  if (editorView && content !== editorView.state.doc.toString()) {
    const transaction = editorView.state.update({
      changes: {
        from: 0,
        to: editorView.state.doc.length,
        insert: content
      }
    })
    editorView.dispatch(transaction)
  }
}

const handleFormat = async () => {
  if (!props.modelValue.trim()) {
    message.warning('请先输入 SQL 语句')
    return
  }

  formatting.value = true
  
  try {
    // 简单的SQL格式化逻辑
    const formatted = formatSql(props.modelValue)
    emit('update:modelValue', formatted)
    emit('format', formatted)
    message.success('SQL 格式化完成')
  } catch (error) {
    message.error('SQL 格式化失败')
    console.error('Format error:', error)
  } finally {
    formatting.value = false
  }
}

const handleClear = () => {
  emit('update:modelValue', '')
  emit('clear')
  message.info('已清空 SQL 内容')
}

const handleCopy = async () => {
  if (!props.modelValue) return
  
  try {
    await navigator.clipboard.writeText(props.modelValue)
    message.success('已复制到剪贴板')
  } catch (error) {
    message.error('复制失败')
    console.error('Copy error:', error)
  }
}

// 简单的SQL格式化函数
const formatSql = (sql: string): string => {
  return sql
    .replace(/\s+/g, ' ') // 合并多个空格
    .replace(/,\s*/g, ',\n  ') // 逗号后换行并缩进
    .replace(/\b(SELECT|FROM|WHERE|JOIN|INNER JOIN|LEFT JOIN|RIGHT JOIN|FULL JOIN|GROUP BY|ORDER BY|HAVING|UNION|INSERT|UPDATE|DELETE|CREATE|ALTER|DROP)\b/gi, '\n$1') // 关键字前换行
    .replace(/\b(AND|OR)\b/gi, '\n  $1') // AND/OR 换行并缩进
    .replace(/\(/g, '(\n  ') // 左括号后换行缩进
    .replace(/\)/g, '\n)') // 右括号前换行
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0)
    .join('\n')
    .trim()
}

// Watchers
watch(() => props.modelValue, (newValue) => {
  updateEditorContent(newValue)
})

watch(() => props.theme, () => {
  // 主题变化时重新初始化编辑器
  destroyEditor()
  nextTick(() => {
    initEditor()
  })
})

// Lifecycle
onMounted(() => {
  nextTick(() => {
    initEditor()
  })
})

onUnmounted(() => {
  destroyEditor()
})
</script>

<style scoped>
.sql-editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: #fff;
  overflow: hidden;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.editor-label {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.editor-wrapper {
  flex: 1;
  min-height: 0;
  position: relative;
}

.editor-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  background: #fafafa;
  border-top: 1px solid #f0f0f0;
  font-size: 12px;
  color: #8c8c8c;
  flex-shrink: 0;
}

.footer-left,
.footer-right {
  display: flex;
  align-items: center;
}

.status-text {
  display: flex;
  align-items: center;
  gap: 4px;
}

.cursor-position {
  font-family: Monaco, Menlo, "Ubuntu Mono", monospace;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    padding: 12px;
  }
  
  .header-right {
    width: 100%;
    justify-content: flex-end;
  }
  
  .editor-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .footer-right {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
