import { Extension } from '@codemirror/state';
import { HighlightStyle } from '@codemirror/language';

/**
The colors used in the theme, as CSS color strings.
*/
declare const color: {
    chalky: string;
    coral: string;
    cyan: string;
    invalid: string;
    ivory: string;
    stone: string;
    malibu: string;
    sage: string;
    whiskey: string;
    violet: string;
    darkBackground: string;
    highlightBackground: string;
    background: string;
    tooltipBackground: string;
    selection: string;
    cursor: string;
};
/**
The editor theme styles for One Dark.
*/
declare const oneDarkTheme: Extension;
/**
The highlighting style for code in the One Dark theme.
*/
declare const oneDarkHighlightStyle: HighlightStyle;
/**
Extension to enable the One Dark theme (both the editor theme and
the highlight style).
*/
declare const oneDark: Extension;

export { color, oneDark, oneDarkHighlightStyle, oneDarkTheme };
