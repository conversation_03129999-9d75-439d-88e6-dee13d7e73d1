declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    prefixCls: StringConstructor;
    presets: {
        type: ArrayConstructor;
        default: () => any[];
    };
    onClick: FunctionConstructor;
    onHover: FunctionConstructor;
}>, () => import("vue/jsx-runtime").JSX.Element, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    prefixCls: StringConstructor;
    presets: {
        type: ArrayConstructor;
        default: () => any[];
    };
    onClick: FunctionConstructor;
    onHover: FunctionConstructor;
}>> & Readonly<{}>, {
    presets: unknown[];
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
