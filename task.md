# 字段级别数据血缘图组件开发任务清单

## 项目概述
开发一个基于 Vue 3 + Vite + AntV G6 的字段级别数据血缘图组件，用于商业数据治理平台。

## 技术栈
- 前端框架：Vue 3 + Composition API
- 构建工具：Vite
- 图形库：AntV G6
- UI组件库：Ant Design Vue
- 状态管理：Pinia
- 类型检查：TypeScript

## 开发任务清单

### 阶段一：项目初始化与基础架构搭建

#### 任务1：项目初始化 ✅
- [x] 使用 Vite 创建 Vue 3 + TypeScript 项目
- [x] 配置项目基础结构
- [x] 安装必要依赖包（Vue 3、Vite、TypeScript、AntV G6、Ant Design Vue、Pinia等）
- [x] 配置 Vite 构建配置
- [x] 验证项目能正常启动和构建

#### 任务2：项目目录结构搭建 ✅
- [x] 创建组件目录结构（components、store、utils、types等）
- [x] 创建基础的 TypeScript 类型定义文件
- [x] 配置路径别名和导入配置
- [x] 创建基础的样式文件结构

#### 任务3：状态管理初始化 ✅
- [x] 配置 Pinia 状态管理
- [x] 创建血缘图数据状态管理 store
- [x] 定义基础的状态接口和方法
- [x] 验证状态管理正常工作

### 阶段二：基础UI布局实现

#### 任务4：主界面布局组件 🔄
- [ ] 创建主应用布局（左右分栏）
- [ ] 实现响应式布局设计
- [ ] 集成 Ant Design Vue 组件库
- [ ] 验证布局在不同屏幕尺寸下的表现

#### 任务5：左侧工具栏和SQL编辑器
- [ ] 实现上部工具栏（数据库类型选择、美化SQL、解析血缘按钮）
- [ ] 集成SQL编辑器组件（支持语法高亮）
- [ ] 实现基础的SQL格式化功能
- [ ] 验证编辑器功能正常

#### 任务6：右侧控制面板
- [ ] 实现上部控制开关（字段级血缘关系、完整血缘链路）
- [ ] 创建图谱容器区域
- [ ] 实现基础的控制逻辑
- [ ] 验证控制面板交互正常

### 阶段三：G6图谱核心功能实现

#### 任务7：G6图谱初始化
- [ ] 集成 AntV G6 到 Vue 组件中
- [ ] 配置基础的图谱实例
- [ ] 实现图谱容器的响应式调整
- [ ] 验证G6图谱能正常渲染

#### 任务8：自定义表节点注册
- [ ] 使用 G6.registerNode 注册卡片式表节点
- [ ] 实现表名标题区域渲染
- [ ] 实现字段列表纵向排列
- [ ] 动态计算节点高度（根据字段数量）
- [ ] 验证自定义节点正常显示

#### 任务9：字段级连线实现
- [ ] 实现字段到字段的精确连接
- [ ] 支持贝塞尔曲线或直角折线样式
- [ ] 实现连线动画箭头效果
- [ ] 避免连线交叉的布局优化
- [ ] 验证字段连线正确显示

### 阶段四：交互功能实现

#### 任务10：基础交互功能
- [ ] 实现鼠标滚轮缩放功能
- [ ] 实现拖动图谱功能
- [ ] 实现节点拖动调整布局
- [ ] 验证基础交互功能正常

#### 任务11：字段悬浮和点击交互
- [ ] 实现字段悬浮高亮效果
- [ ] 创建字段 Tooltip 组件
- [ ] 实现字段点击详情面板
- [ ] 实现路径追踪高亮功能
- [ ] 验证字段交互功能正常

#### 任务12：高级交互功能
- [ ] 实现搜索定位功能（字段名/表名搜索）
- [ ] 集成 MiniMap 缩略图导航
- [ ] 实现图谱导出功能（PNG/PDF）
- [ ] 实现重置布局功能
- [ ] 验证高级交互功能正常

### 阶段五：数据处理与解析

#### 任务13：数据转换工具
- [ ] 实现原始JSON到G6节点/边的数据转换
- [ ] 创建SQL解析工具（基础版本）
- [ ] 实现血缘关系数据结构处理
- [ ] 验证数据转换功能正常

#### 任务14：布局算法优化
- [ ] 集成 dagre 布局算法
- [ ] 配置从左到右的布局方向
- [ ] 实现布局参数可配置
- [ ] 优化大数据量时的布局性能
- [ ] 验证布局算法效果

### 阶段六：样式优化与用户体验

#### 任务15：视觉样式优化
- [ ] 实现扁平化现代设计风格
- [ ] 优化表节点卡片样式（圆角、阴影）
- [ ] 实现字段高亮和选中状态样式
- [ ] 优化连线样式和动画效果
- [ ] 验证视觉效果符合设计要求

#### 任务16：性能优化
- [ ] 实现大数据量时的虚拟渲染
- [ ] 使用 throttle 优化拖动性能
- [ ] 实现懒加载血缘图功能
- [ ] 优化内存使用和渲染性能
- [ ] 验证性能优化效果

### 阶段七：功能完善与测试

#### 任务17：功能扩展
- [ ] 实现字段筛选功能
- [ ] 支持深色主题切换
- [ ] 实现配置项可定制化
- [ ] 预留后端接口对接能力
- [ ] 验证扩展功能正常

#### 任务18：错误处理与边界情况
- [ ] 实现错误边界处理
- [ ] 处理空数据和异常数据情况
- [ ] 实现加载状态和错误提示
- [ ] 优化用户体验细节
- [ ] 验证错误处理机制

#### 任务19：文档和示例
- [ ] 编写组件使用文档
- [ ] 创建示例数据和演示
- [ ] 编写API接口文档
- [ ] 创建开发指南
- [ ] 验证文档完整性

#### 任务20：最终测试与优化
- [ ] 进行全功能集成测试
- [ ] 性能测试和优化
- [ ] 兼容性测试
- [ ] 用户体验测试
- [ ] 最终代码审查和优化

## 已完成工作总结

### ✅ 已完成的核心文件
1. **类型定义文件** (`src/types/lineage.ts`)
   - 定义了完整的血缘图相关类型接口
   - 包含数据库类型枚举、节点边接口、G6数据接口等

2. **数据转换工具** (`src/utils/graphDataTransform.ts`)
   - 实现血缘数据到G6图数据的转换
   - 提供节点尺寸计算、字段类型颜色等工具函数
   - 包含防抖节流等性能优化函数

3. **SQL解析工具** (`src/utils/sqlParser.ts`)
   - 基础SQL格式化功能
   - 简单的表名和字段名提取
   - 创建示例血缘数据的功能

4. **G6节点注册工具** (`src/utils/registerFieldNode.ts`)
   - 定义了节点样式配置接口
   - 预留了自定义表节点注册的框架（待G6集成时完善）

5. **状态管理Store** (`src/stores/lineageStore.ts`)
   - 完整的Pinia状态管理实现
   - 包含血缘数据、图谱配置、交互状态等
   - 提供数据操作、搜索、主题切换等方法

### 🔧 技术架构已就绪
- ✅ Vue 3 + Composition API + TypeScript
- ✅ Vite 构建工具配置
- ✅ Pinia 状态管理
- ✅ 路径别名配置
- ✅ AntV G6、Ant Design Vue、dagre 依赖包安装

### 📁 项目结构
```
src/
├── types/lineage.ts          # 类型定义
├── utils/
│   ├── graphDataTransform.ts # 数据转换工具
│   ├── sqlParser.ts          # SQL解析工具
│   └── registerFieldNode.ts  # G6节点注册工具
├── stores/lineageStore.ts    # 状态管理
└── ... (Vue默认文件)
```

## 验收标准
每个任务完成后必须满足：
1. 代码能正常构建（npm run build）✅
2. 开发服务器能正常启动（npm run dev）✅
3. 新增功能按预期工作
4. 无TypeScript类型错误 ✅
5. 无控制台错误或警告

## 下一步工作
继续执行**任务4：主界面布局组件**，开始实现UI界面。
