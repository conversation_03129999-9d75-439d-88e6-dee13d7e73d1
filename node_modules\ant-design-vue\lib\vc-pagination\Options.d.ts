declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    changeSize: FunctionConstructor;
    quickGo: FunctionConstructor;
    selectComponentClass: import("vue-types").VueTypeValidableDef<any>;
    current: NumberConstructor;
    pageSizeOptions: import("vue-types").VueTypeValidableDef<unknown[]> & {
        default: () => unknown[];
    } & {
        default: () => unknown[];
    };
    pageSize: NumberConstructor;
    buildOptionText: FunctionConstructor;
    locale: import("vue-types").VueTypeValidableDef<{
        [key: string]: any;
    }> & {
        default: () => {
            [key: string]: any;
        };
    };
    rootPrefixCls: StringConstructor;
    selectPrefixCls: StringConstructor;
    goButton: import("vue-types").VueTypeValidableDef<any>;
}>, () => import("vue/jsx-runtime").JSX.Element, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    changeSize: FunctionConstructor;
    quickGo: FunctionConstructor;
    selectComponentClass: import("vue-types").VueTypeValidableDef<any>;
    current: NumberConstructor;
    pageSizeOptions: import("vue-types").VueTypeValidableDef<unknown[]> & {
        default: () => unknown[];
    } & {
        default: () => unknown[];
    };
    pageSize: NumberConstructor;
    buildOptionText: FunctionConstructor;
    locale: import("vue-types").VueTypeValidableDef<{
        [key: string]: any;
    }> & {
        default: () => {
            [key: string]: any;
        };
    };
    rootPrefixCls: StringConstructor;
    selectPrefixCls: StringConstructor;
    goButton: import("vue-types").VueTypeValidableDef<any>;
}>> & Readonly<{}>, {
    disabled: boolean;
    locale: {
        [key: string]: any;
    };
    pageSizeOptions: unknown[];
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
