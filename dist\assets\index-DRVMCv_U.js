const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/AboutView-ltg8wc-7.js","assets/AboutView-CSIvawM9.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();/**
* @vue/shared v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ls(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const te={},Rt=[],Ve=()=>{},Qo=()=>!1,bn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),cs=e=>e.startsWith("onUpdate:"),fe=Object.assign,us=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Jo=Object.prototype.hasOwnProperty,q=(e,t)=>Jo.call(e,t),F=Array.isArray,Ct=e=>wn(e)==="[object Map]",Rr=e=>wn(e)==="[object Set]",N=e=>typeof e=="function",re=e=>typeof e=="string",Xe=e=>typeof e=="symbol",se=e=>e!==null&&typeof e=="object",Cr=e=>(se(e)||N(e))&&N(e.then)&&N(e.catch),Pr=Object.prototype.toString,wn=e=>Pr.call(e),Xo=e=>wn(e).slice(8,-1),Ar=e=>wn(e)==="[object Object]",fs=e=>re(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Dt=ls(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),xn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Zo=/-(\w)/g,ut=xn(e=>e.replace(Zo,(t,n)=>n?n.toUpperCase():"")),ei=/\B([A-Z])/g,_t=xn(e=>e.replace(ei,"-$1").toLowerCase()),Or=xn(e=>e.charAt(0).toUpperCase()+e.slice(1)),Mn=xn(e=>e?`on${Or(e)}`:""),ct=(e,t)=>!Object.is(e,t),Tn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Kn=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},ti=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Is;const En=()=>Is||(Is=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function as(e){if(F(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=re(s)?oi(s):as(s);if(r)for(const o in r)t[o]=r[o]}return t}else if(re(e)||se(e))return e}const ni=/;(?![^(]*\))/g,si=/:([^]+)/,ri=/\/\*[^]*?\*\//g;function oi(e){const t={};return e.replace(ri,"").split(ni).forEach(n=>{if(n){const s=n.split(si);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function ds(e){let t="";if(re(e))t=e;else if(F(e))for(let n=0;n<e.length;n++){const s=ds(e[n]);s&&(t+=s+" ")}else if(se(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const ii="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",li=ls(ii);function Mr(e){return!!e||e===""}const Tr=e=>!!(e&&e.__v_isRef===!0),Ir=e=>re(e)?e:e==null?"":F(e)||se(e)&&(e.toString===Pr||!N(e.toString))?Tr(e)?Ir(e.value):JSON.stringify(e,zr,2):String(e),zr=(e,t)=>Tr(t)?zr(e,t.value):Ct(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],o)=>(n[In(s,o)+" =>"]=r,n),{})}:Rr(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>In(n))}:Xe(t)?In(t):se(t)&&!F(t)&&!Ar(t)?String(t):t,In=(e,t="")=>{var n;return Xe(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let _e;class Hr{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=_e,!t&&_e&&(this.index=(_e.scopes||(_e.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=_e;try{return _e=this,t()}finally{_e=n}}}on(){++this._on===1&&(this.prevScope=_e,_e=this)}off(){this._on>0&&--this._on===0&&(_e=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function ci(e){return new Hr(e)}function ui(){return _e}let ee;const zn=new WeakSet;class $r{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,_e&&_e.active&&_e.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,zn.has(this)&&(zn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||jr(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,zs(this),Fr(this);const t=ee,n=Pe;ee=this,Pe=!0;try{return this.fn()}finally{Nr(this),ee=t,Pe=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)gs(t);this.deps=this.depsTail=void 0,zs(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?zn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Wn(this)&&this.run()}get dirty(){return Wn(this)}}let Lr=0,kt,Bt;function jr(e,t=!1){if(e.flags|=8,t){e.next=Bt,Bt=e;return}e.next=kt,kt=e}function hs(){Lr++}function ps(){if(--Lr>0)return;if(Bt){let t=Bt;for(Bt=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;kt;){let t=kt;for(kt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Fr(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Nr(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),gs(s),fi(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function Wn(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Vr(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Vr(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Qt)||(e.globalVersion=Qt,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Wn(e))))return;e.flags|=2;const t=e.dep,n=ee,s=Pe;ee=e,Pe=!0;try{Fr(e);const r=e.fn(e._value);(t.version===0||ct(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{ee=n,Pe=s,Nr(e),e.flags&=-3}}function gs(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)gs(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function fi(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Pe=!0;const Dr=[];function Qe(){Dr.push(Pe),Pe=!1}function Je(){const e=Dr.pop();Pe=e===void 0?!0:e}function zs(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ee;ee=void 0;try{t()}finally{ee=n}}}let Qt=0;class ai{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class ms{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!ee||!Pe||ee===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ee)n=this.activeLink=new ai(ee,this),ee.deps?(n.prevDep=ee.depsTail,ee.depsTail.nextDep=n,ee.depsTail=n):ee.deps=ee.depsTail=n,kr(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=ee.depsTail,n.nextDep=void 0,ee.depsTail.nextDep=n,ee.depsTail=n,ee.deps===n&&(ee.deps=s)}return n}trigger(t){this.version++,Qt++,this.notify(t)}notify(t){hs();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{ps()}}}function kr(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)kr(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const qn=new WeakMap,gt=Symbol(""),Gn=Symbol(""),Jt=Symbol("");function ce(e,t,n){if(Pe&&ee){let s=qn.get(e);s||qn.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new ms),r.map=s,r.key=n),r.track()}}function qe(e,t,n,s,r,o){const i=qn.get(e);if(!i){Qt++;return}const l=c=>{c&&c.trigger()};if(hs(),t==="clear")i.forEach(l);else{const c=F(e),h=c&&fs(n);if(c&&n==="length"){const a=Number(s);i.forEach((d,g)=>{(g==="length"||g===Jt||!Xe(g)&&g>=a)&&l(d)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),h&&l(i.get(Jt)),t){case"add":c?h&&l(i.get("length")):(l(i.get(gt)),Ct(e)&&l(i.get(Gn)));break;case"delete":c||(l(i.get(gt)),Ct(e)&&l(i.get(Gn)));break;case"set":Ct(e)&&l(i.get(gt));break}}ps()}function xt(e){const t=W(e);return t===e?t:(ce(t,"iterate",Jt),Ae(e)?t:t.map(de))}function vs(e){return ce(e=W(e),"iterate",Jt),e}const di={__proto__:null,[Symbol.iterator](){return Hn(this,Symbol.iterator,de)},concat(...e){return xt(this).concat(...e.map(t=>F(t)?xt(t):t))},entries(){return Hn(this,"entries",e=>(e[1]=de(e[1]),e))},every(e,t){return Ue(this,"every",e,t,void 0,arguments)},filter(e,t){return Ue(this,"filter",e,t,n=>n.map(de),arguments)},find(e,t){return Ue(this,"find",e,t,de,arguments)},findIndex(e,t){return Ue(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ue(this,"findLast",e,t,de,arguments)},findLastIndex(e,t){return Ue(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ue(this,"forEach",e,t,void 0,arguments)},includes(...e){return $n(this,"includes",e)},indexOf(...e){return $n(this,"indexOf",e)},join(e){return xt(this).join(e)},lastIndexOf(...e){return $n(this,"lastIndexOf",e)},map(e,t){return Ue(this,"map",e,t,void 0,arguments)},pop(){return Lt(this,"pop")},push(...e){return Lt(this,"push",e)},reduce(e,...t){return Hs(this,"reduce",e,t)},reduceRight(e,...t){return Hs(this,"reduceRight",e,t)},shift(){return Lt(this,"shift")},some(e,t){return Ue(this,"some",e,t,void 0,arguments)},splice(...e){return Lt(this,"splice",e)},toReversed(){return xt(this).toReversed()},toSorted(e){return xt(this).toSorted(e)},toSpliced(...e){return xt(this).toSpliced(...e)},unshift(...e){return Lt(this,"unshift",e)},values(){return Hn(this,"values",de)}};function Hn(e,t,n){const s=vs(e),r=s[t]();return s!==e&&!Ae(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=n(o.value)),o}),r}const hi=Array.prototype;function Ue(e,t,n,s,r,o){const i=vs(e),l=i!==e&&!Ae(e),c=i[t];if(c!==hi[t]){const d=c.apply(e,o);return l?de(d):d}let h=n;i!==e&&(l?h=function(d,g){return n.call(this,de(d),g,e)}:n.length>2&&(h=function(d,g){return n.call(this,d,g,e)}));const a=c.call(i,h,s);return l&&r?r(a):a}function Hs(e,t,n,s){const r=vs(e);let o=n;return r!==e&&(Ae(e)?n.length>3&&(o=function(i,l,c){return n.call(this,i,l,c,e)}):o=function(i,l,c){return n.call(this,i,de(l),c,e)}),r[t](o,...s)}function $n(e,t,n){const s=W(e);ce(s,"iterate",Jt);const r=s[t](...n);return(r===-1||r===!1)&&bs(n[0])?(n[0]=W(n[0]),s[t](...n)):r}function Lt(e,t,n=[]){Qe(),hs();const s=W(e)[t].apply(e,n);return ps(),Je(),s}const pi=ls("__proto__,__v_isRef,__isVue"),Br=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Xe));function gi(e){Xe(e)||(e=String(e));const t=W(this);return ce(t,"has",e),t.hasOwnProperty(e)}class Ur{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(r?o?Ri:Gr:o?qr:Wr).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=F(t);if(!r){let c;if(i&&(c=di[n]))return c;if(n==="hasOwnProperty")return gi}const l=Reflect.get(t,n,ue(t)?t:s);return(Xe(n)?Br.has(n):pi(n))||(r||ce(t,"get",n),o)?l:ue(l)?i&&fs(n)?l:l.value:se(l)?r?Qr(l):Sn(l):l}}class Kr extends Ur{constructor(t=!1){super(!1,t)}set(t,n,s,r){let o=t[n];if(!this._isShallow){const c=mt(o);if(!Ae(s)&&!mt(s)&&(o=W(o),s=W(s)),!F(t)&&ue(o)&&!ue(s))return c?!1:(o.value=s,!0)}const i=F(t)&&fs(n)?Number(n)<t.length:q(t,n),l=Reflect.set(t,n,s,ue(t)?t:r);return t===W(r)&&(i?ct(s,o)&&qe(t,"set",n,s):qe(t,"add",n,s)),l}deleteProperty(t,n){const s=q(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&qe(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!Xe(n)||!Br.has(n))&&ce(t,"has",n),s}ownKeys(t){return ce(t,"iterate",F(t)?"length":gt),Reflect.ownKeys(t)}}class mi extends Ur{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const vi=new Kr,_i=new mi,yi=new Kr(!0);const Yn=e=>e,cn=e=>Reflect.getPrototypeOf(e);function bi(e,t,n){return function(...s){const r=this.__v_raw,o=W(r),i=Ct(o),l=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,h=r[e](...s),a=n?Yn:t?Qn:de;return!t&&ce(o,"iterate",c?Gn:gt),{next(){const{value:d,done:g}=h.next();return g?{value:d,done:g}:{value:l?[a(d[0]),a(d[1])]:a(d),done:g}},[Symbol.iterator](){return this}}}}function un(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function wi(e,t){const n={get(r){const o=this.__v_raw,i=W(o),l=W(r);e||(ct(r,l)&&ce(i,"get",r),ce(i,"get",l));const{has:c}=cn(i),h=t?Yn:e?Qn:de;if(c.call(i,r))return h(o.get(r));if(c.call(i,l))return h(o.get(l));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&ce(W(r),"iterate",gt),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=W(o),l=W(r);return e||(ct(r,l)&&ce(i,"has",r),ce(i,"has",l)),r===l?o.has(r):o.has(r)||o.has(l)},forEach(r,o){const i=this,l=i.__v_raw,c=W(l),h=t?Yn:e?Qn:de;return!e&&ce(c,"iterate",gt),l.forEach((a,d)=>r.call(o,h(a),h(d),i))}};return fe(n,e?{add:un("add"),set:un("set"),delete:un("delete"),clear:un("clear")}:{add(r){!t&&!Ae(r)&&!mt(r)&&(r=W(r));const o=W(this);return cn(o).has.call(o,r)||(o.add(r),qe(o,"add",r,r)),this},set(r,o){!t&&!Ae(o)&&!mt(o)&&(o=W(o));const i=W(this),{has:l,get:c}=cn(i);let h=l.call(i,r);h||(r=W(r),h=l.call(i,r));const a=c.call(i,r);return i.set(r,o),h?ct(o,a)&&qe(i,"set",r,o):qe(i,"add",r,o),this},delete(r){const o=W(this),{has:i,get:l}=cn(o);let c=i.call(o,r);c||(r=W(r),c=i.call(o,r)),l&&l.call(o,r);const h=o.delete(r);return c&&qe(o,"delete",r,void 0),h},clear(){const r=W(this),o=r.size!==0,i=r.clear();return o&&qe(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=bi(r,e,t)}),n}function _s(e,t){const n=wi(e,t);return(s,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(q(n,r)&&r in s?n:s,r,o)}const xi={get:_s(!1,!1)},Ei={get:_s(!1,!0)},Si={get:_s(!0,!1)};const Wr=new WeakMap,qr=new WeakMap,Gr=new WeakMap,Ri=new WeakMap;function Ci(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Pi(e){return e.__v_skip||!Object.isExtensible(e)?0:Ci(Xo(e))}function Sn(e){return mt(e)?e:ys(e,!1,vi,xi,Wr)}function Yr(e){return ys(e,!1,yi,Ei,qr)}function Qr(e){return ys(e,!0,_i,Si,Gr)}function ys(e,t,n,s,r){if(!se(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=Pi(e);if(o===0)return e;const i=r.get(e);if(i)return i;const l=new Proxy(e,o===2?s:n);return r.set(e,l),l}function Ut(e){return mt(e)?Ut(e.__v_raw):!!(e&&e.__v_isReactive)}function mt(e){return!!(e&&e.__v_isReadonly)}function Ae(e){return!!(e&&e.__v_isShallow)}function bs(e){return e?!!e.__v_raw:!1}function W(e){const t=e&&e.__v_raw;return t?W(t):e}function Jr(e){return!q(e,"__v_skip")&&Object.isExtensible(e)&&Kn(e,"__v_skip",!0),e}const de=e=>se(e)?Sn(e):e,Qn=e=>se(e)?Qr(e):e;function ue(e){return e?e.__v_isRef===!0:!1}function Xr(e){return Zr(e,!1)}function Ai(e){return Zr(e,!0)}function Zr(e,t){return ue(e)?e:new Oi(e,t)}class Oi{constructor(t,n){this.dep=new ms,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:W(t),this._value=n?t:de(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Ae(t)||mt(t);t=s?t:W(t),ct(t,n)&&(this._rawValue=t,this._value=s?t:de(t),this.dep.trigger())}}function Ge(e){return ue(e)?e.value:e}const Mi={get:(e,t,n)=>t==="__v_raw"?e:Ge(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return ue(r)&&!ue(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function eo(e){return Ut(e)?e:new Proxy(e,Mi)}class Ti{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new ms(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Qt-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&ee!==this)return jr(this,!0),!0}get value(){const t=this.dep.track();return Vr(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Ii(e,t,n=!1){let s,r;return N(e)?s=e:(s=e.get,r=e.set),new Ti(s,r,n)}const fn={},gn=new WeakMap;let pt;function zi(e,t=!1,n=pt){if(n){let s=gn.get(n);s||gn.set(n,s=[]),s.push(e)}}function Hi(e,t,n=te){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:l,call:c}=n,h=M=>r?M:Ae(M)||r===!1||r===0?lt(M,1):lt(M);let a,d,g,m,A=!1,O=!1;if(ue(e)?(d=()=>e.value,A=Ae(e)):Ut(e)?(d=()=>h(e),A=!0):F(e)?(O=!0,A=e.some(M=>Ut(M)||Ae(M)),d=()=>e.map(M=>{if(ue(M))return M.value;if(Ut(M))return h(M);if(N(M))return c?c(M,2):M()})):N(e)?t?d=c?()=>c(e,2):e:d=()=>{if(g){Qe();try{g()}finally{Je()}}const M=pt;pt=a;try{return c?c(e,3,[m]):e(m)}finally{pt=M}}:d=Ve,t&&r){const M=d,J=r===!0?1/0:r;d=()=>lt(M(),J)}const V=ui(),$=()=>{a.stop(),V&&V.active&&us(V.effects,a)};if(o&&t){const M=t;t=(...J)=>{M(...J),$()}}let I=O?new Array(e.length).fill(fn):fn;const L=M=>{if(!(!(a.flags&1)||!a.dirty&&!M))if(t){const J=a.run();if(r||A||(O?J.some((le,ne)=>ct(le,I[ne])):ct(J,I))){g&&g();const le=pt;pt=a;try{const ne=[J,I===fn?void 0:O&&I[0]===fn?[]:I,m];I=J,c?c(t,3,ne):t(...ne)}finally{pt=le}}}else a.run()};return l&&l(L),a=new $r(d),a.scheduler=i?()=>i(L,!1):L,m=M=>zi(M,!1,a),g=a.onStop=()=>{const M=gn.get(a);if(M){if(c)c(M,4);else for(const J of M)J();gn.delete(a)}},t?s?L(!0):I=a.run():i?i(L.bind(null,!0),!0):a.run(),$.pause=a.pause.bind(a),$.resume=a.resume.bind(a),$.stop=$,$}function lt(e,t=1/0,n){if(t<=0||!se(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ue(e))lt(e.value,t,n);else if(F(e))for(let s=0;s<e.length;s++)lt(e[s],t,n);else if(Rr(e)||Ct(e))e.forEach(s=>{lt(s,t,n)});else if(Ar(e)){for(const s in e)lt(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&lt(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function rn(e,t,n,s){try{return s?e(...s):e()}catch(r){Rn(r,t,n)}}function De(e,t,n,s){if(N(e)){const r=rn(e,t,n,s);return r&&Cr(r)&&r.catch(o=>{Rn(o,t,n)}),r}if(F(e)){const r=[];for(let o=0;o<e.length;o++)r.push(De(e[o],t,n,s));return r}}function Rn(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||te;if(t){let l=t.parent;const c=t.proxy,h=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const a=l.ec;if(a){for(let d=0;d<a.length;d++)if(a[d](e,c,h)===!1)return}l=l.parent}if(o){Qe(),rn(o,null,10,[e,c,h]),Je();return}}$i(e,n,r,s,i)}function $i(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const he=[];let Fe=-1;const Pt=[];let rt=null,Et=0;const to=Promise.resolve();let mn=null;function no(e){const t=mn||to;return e?t.then(this?e.bind(this):e):t}function Li(e){let t=Fe+1,n=he.length;for(;t<n;){const s=t+n>>>1,r=he[s],o=Xt(r);o<e||o===e&&r.flags&2?t=s+1:n=s}return t}function ws(e){if(!(e.flags&1)){const t=Xt(e),n=he[he.length-1];!n||!(e.flags&2)&&t>=Xt(n)?he.push(e):he.splice(Li(t),0,e),e.flags|=1,so()}}function so(){mn||(mn=to.then(oo))}function ji(e){F(e)?Pt.push(...e):rt&&e.id===-1?rt.splice(Et+1,0,e):e.flags&1||(Pt.push(e),e.flags|=1),so()}function $s(e,t,n=Fe+1){for(;n<he.length;n++){const s=he[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;he.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function ro(e){if(Pt.length){const t=[...new Set(Pt)].sort((n,s)=>Xt(n)-Xt(s));if(Pt.length=0,rt){rt.push(...t);return}for(rt=t,Et=0;Et<rt.length;Et++){const n=rt[Et];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}rt=null,Et=0}}const Xt=e=>e.id==null?e.flags&2?-1:1/0:e.id;function oo(e){try{for(Fe=0;Fe<he.length;Fe++){const t=he[Fe];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),rn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Fe<he.length;Fe++){const t=he[Fe];t&&(t.flags&=-2)}Fe=-1,he.length=0,ro(),mn=null,(he.length||Pt.length)&&oo()}}let be=null,io=null;function vn(e){const t=be;return be=e,io=e&&e.type.__scopeId||null,t}function ie(e,t=be,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&Us(-1);const o=vn(t);let i;try{i=e(...r)}finally{vn(o),s._d&&Us(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function dt(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];o&&(l.oldValue=o[i].value);let c=l.dir[s];c&&(Qe(),De(c,n,8,[e.el,l,e,t]),Je())}}const Fi=Symbol("_vte"),Ni=e=>e.__isTeleport;function xs(e,t){e.shapeFlag&6&&e.component?(e.transition=t,xs(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function zt(e,t){return N(e)?fe({name:e.name},t,{setup:e}):e}function lo(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Kt(e,t,n,s,r=!1){if(F(e)){e.forEach((A,O)=>Kt(A,t&&(F(t)?t[O]:t),n,s,r));return}if(At(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&Kt(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?Cs(s.component):s.el,i=r?null:o,{i:l,r:c}=e,h=t&&t.r,a=l.refs===te?l.refs={}:l.refs,d=l.setupState,g=W(d),m=d===te?()=>!1:A=>q(g,A);if(h!=null&&h!==c&&(re(h)?(a[h]=null,m(h)&&(d[h]=null)):ue(h)&&(h.value=null)),N(c))rn(c,l,12,[i,a]);else{const A=re(c),O=ue(c);if(A||O){const V=()=>{if(e.f){const $=A?m(c)?d[c]:a[c]:c.value;r?F($)&&us($,o):F($)?$.includes(o)||$.push(o):A?(a[c]=[o],m(c)&&(d[c]=a[c])):(c.value=[o],e.k&&(a[e.k]=c.value))}else A?(a[c]=i,m(c)&&(d[c]=i)):O&&(c.value=i,e.k&&(a[e.k]=i))};i?(V.id=-1,xe(V,n)):V()}}}En().requestIdleCallback;En().cancelIdleCallback;const At=e=>!!e.type.__asyncLoader,co=e=>e.type.__isKeepAlive;function Vi(e,t){uo(e,"a",t)}function Di(e,t){uo(e,"da",t)}function uo(e,t,n=pe){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Cn(t,s,n),n){let r=n.parent;for(;r&&r.parent;)co(r.parent.vnode)&&ki(s,t,n,r),r=r.parent}}function ki(e,t,n,s){const r=Cn(t,e,s,!0);fo(()=>{us(s[t],r)},n)}function Cn(e,t,n=pe,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{Qe();const l=on(n),c=De(t,n,e,i);return l(),Je(),c});return s?r.unshift(o):r.push(o),o}}const Ze=e=>(t,n=pe)=>{(!tn||e==="sp")&&Cn(e,(...s)=>t(...s),n)},Bi=Ze("bm"),Ui=Ze("m"),Ki=Ze("bu"),Wi=Ze("u"),qi=Ze("bum"),fo=Ze("um"),Gi=Ze("sp"),Yi=Ze("rtg"),Qi=Ze("rtc");function Ji(e,t=pe){Cn("ec",e,t)}const Xi=Symbol.for("v-ndc");function Ln(e,t,n={},s,r){if(be.ce||be.parent&&At(be.parent)&&be.parent.ce)return t!=="default"&&(n.name=t),Re(),Ks(ye,null,[Q("slot",n,s)],64);let o=e[t];o&&o._c&&(o._d=!1),Re();const i=o&&ao(o(n)),l=n.key||i&&i.key,c=Ks(ye,{key:(l&&!Xe(l)?l:`_${t}`)+""},i||[],i&&e._===1?64:-2);return o&&o._c&&(o._d=!0),c}function ao(e){return e.some(t=>en(t)?!(t.type===vt||t.type===ye&&!ao(t.children)):!0)?e:null}const Jn=e=>e?Io(e)?Cs(e):Jn(e.parent):null,Wt=fe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Jn(e.parent),$root:e=>Jn(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>po(e),$forceUpdate:e=>e.f||(e.f=()=>{ws(e.update)}),$nextTick:e=>e.n||(e.n=no.bind(e.proxy)),$watch:e=>yl.bind(e)}),jn=(e,t)=>e!==te&&!e.__isScriptSetup&&q(e,t),Zi={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:l,appContext:c}=e;let h;if(t[0]!=="$"){const m=i[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(jn(s,t))return i[t]=1,s[t];if(r!==te&&q(r,t))return i[t]=2,r[t];if((h=e.propsOptions[0])&&q(h,t))return i[t]=3,o[t];if(n!==te&&q(n,t))return i[t]=4,n[t];Xn&&(i[t]=0)}}const a=Wt[t];let d,g;if(a)return t==="$attrs"&&ce(e.attrs,"get",""),a(e);if((d=l.__cssModules)&&(d=d[t]))return d;if(n!==te&&q(n,t))return i[t]=4,n[t];if(g=c.config.globalProperties,q(g,t))return g[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return jn(r,t)?(r[t]=n,!0):s!==te&&q(s,t)?(s[t]=n,!0):q(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let l;return!!n[i]||e!==te&&q(e,i)||jn(t,i)||(l=o[0])&&q(l,i)||q(s,i)||q(Wt,i)||q(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:q(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Ls(e){return F(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Xn=!0;function el(e){const t=po(e),n=e.proxy,s=e.ctx;Xn=!1,t.beforeCreate&&js(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:l,provide:c,inject:h,created:a,beforeMount:d,mounted:g,beforeUpdate:m,updated:A,activated:O,deactivated:V,beforeDestroy:$,beforeUnmount:I,destroyed:L,unmounted:M,render:J,renderTracked:le,renderTriggered:ne,errorCaptured:Me,serverPrefetch:et,expose:Te,inheritAttrs:tt,components:at,directives:Ie,filters:Ht}=t;if(h&&tl(h,s,null),i)for(const Y in i){const U=i[Y];N(U)&&(s[Y]=U.bind(n))}if(r){const Y=r.call(n,n);se(Y)&&(e.data=Sn(Y))}if(Xn=!0,o)for(const Y in o){const U=o[Y],Be=N(U)?U.bind(n,n):N(U.get)?U.get.bind(n,n):Ve,nt=!N(U)&&N(U.set)?U.set.bind(n):Ve,ze=Ce({get:Be,set:nt});Object.defineProperty(s,Y,{enumerable:!0,configurable:!0,get:()=>ze.value,set:ge=>ze.value=ge})}if(l)for(const Y in l)ho(l[Y],s,n,Y);if(c){const Y=N(c)?c.call(n):c;Reflect.ownKeys(Y).forEach(U=>{an(U,Y[U])})}a&&js(a,e,"c");function oe(Y,U){F(U)?U.forEach(Be=>Y(Be.bind(n))):U&&Y(U.bind(n))}if(oe(Bi,d),oe(Ui,g),oe(Ki,m),oe(Wi,A),oe(Vi,O),oe(Di,V),oe(Ji,Me),oe(Qi,le),oe(Yi,ne),oe(qi,I),oe(fo,M),oe(Gi,et),F(Te))if(Te.length){const Y=e.exposed||(e.exposed={});Te.forEach(U=>{Object.defineProperty(Y,U,{get:()=>n[U],set:Be=>n[U]=Be})})}else e.exposed||(e.exposed={});J&&e.render===Ve&&(e.render=J),tt!=null&&(e.inheritAttrs=tt),at&&(e.components=at),Ie&&(e.directives=Ie),et&&lo(e)}function tl(e,t,n=Ve){F(e)&&(e=Zn(e));for(const s in e){const r=e[s];let o;se(r)?"default"in r?o=Ye(r.from||s,r.default,!0):o=Ye(r.from||s):o=Ye(r),ue(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[s]=o}}function js(e,t,n){De(F(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function ho(e,t,n,s){let r=s.includes(".")?Po(n,s):()=>n[s];if(re(e)){const o=t[e];N(o)&&dn(r,o)}else if(N(e))dn(r,e.bind(n));else if(se(e))if(F(e))e.forEach(o=>ho(o,t,n,s));else{const o=N(e.handler)?e.handler.bind(n):t[e.handler];N(o)&&dn(r,o,e)}}function po(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let c;return l?c=l:!r.length&&!n&&!s?c=t:(c={},r.length&&r.forEach(h=>_n(c,h,i,!0)),_n(c,t,i)),se(t)&&o.set(t,c),c}function _n(e,t,n,s=!1){const{mixins:r,extends:o}=t;o&&_n(e,o,n,!0),r&&r.forEach(i=>_n(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const l=nl[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const nl={data:Fs,props:Ns,emits:Ns,methods:Vt,computed:Vt,beforeCreate:ae,created:ae,beforeMount:ae,mounted:ae,beforeUpdate:ae,updated:ae,beforeDestroy:ae,beforeUnmount:ae,destroyed:ae,unmounted:ae,activated:ae,deactivated:ae,errorCaptured:ae,serverPrefetch:ae,components:Vt,directives:Vt,watch:rl,provide:Fs,inject:sl};function Fs(e,t){return t?e?function(){return fe(N(e)?e.call(this,this):e,N(t)?t.call(this,this):t)}:t:e}function sl(e,t){return Vt(Zn(e),Zn(t))}function Zn(e){if(F(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ae(e,t){return e?[...new Set([].concat(e,t))]:t}function Vt(e,t){return e?fe(Object.create(null),e,t):t}function Ns(e,t){return e?F(e)&&F(t)?[...new Set([...e,...t])]:fe(Object.create(null),Ls(e),Ls(t??{})):t}function rl(e,t){if(!e)return t;if(!t)return e;const n=fe(Object.create(null),e);for(const s in t)n[s]=ae(e[s],t[s]);return n}function go(){return{app:null,config:{isNativeTag:Qo,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ol=0;function il(e,t){return function(s,r=null){N(s)||(s=fe({},s)),r!=null&&!se(r)&&(r=null);const o=go(),i=new WeakSet,l=[];let c=!1;const h=o.app={_uid:ol++,_component:s,_props:r,_container:null,_context:o,_instance:null,version:Nl,get config(){return o.config},set config(a){},use(a,...d){return i.has(a)||(a&&N(a.install)?(i.add(a),a.install(h,...d)):N(a)&&(i.add(a),a(h,...d))),h},mixin(a){return o.mixins.includes(a)||o.mixins.push(a),h},component(a,d){return d?(o.components[a]=d,h):o.components[a]},directive(a,d){return d?(o.directives[a]=d,h):o.directives[a]},mount(a,d,g){if(!c){const m=h._ceVNode||Q(s,r);return m.appContext=o,g===!0?g="svg":g===!1&&(g=void 0),e(m,a,g),c=!0,h._container=a,a.__vue_app__=h,Cs(m.component)}},onUnmount(a){l.push(a)},unmount(){c&&(De(l,h._instance,16),e(null,h._container),delete h._container.__vue_app__)},provide(a,d){return o.provides[a]=d,h},runWithContext(a){const d=Ot;Ot=h;try{return a()}finally{Ot=d}}};return h}}let Ot=null;function an(e,t){if(pe){let n=pe.provides;const s=pe.parent&&pe.parent.provides;s===n&&(n=pe.provides=Object.create(s)),n[e]=t}}function Ye(e,t,n=!1){const s=pe||be;if(s||Ot){let r=Ot?Ot._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&N(t)?t.call(s&&s.proxy):t}}const mo={},vo=()=>Object.create(mo),_o=e=>Object.getPrototypeOf(e)===mo;function ll(e,t,n,s=!1){const r={},o=vo();e.propsDefaults=Object.create(null),yo(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:Yr(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function cl(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,l=W(r),[c]=e.propsOptions;let h=!1;if((s||i>0)&&!(i&16)){if(i&8){const a=e.vnode.dynamicProps;for(let d=0;d<a.length;d++){let g=a[d];if(Pn(e.emitsOptions,g))continue;const m=t[g];if(c)if(q(o,g))m!==o[g]&&(o[g]=m,h=!0);else{const A=ut(g);r[A]=es(c,l,A,m,e,!1)}else m!==o[g]&&(o[g]=m,h=!0)}}}else{yo(e,t,r,o)&&(h=!0);let a;for(const d in l)(!t||!q(t,d)&&((a=_t(d))===d||!q(t,a)))&&(c?n&&(n[d]!==void 0||n[a]!==void 0)&&(r[d]=es(c,l,d,void 0,e,!0)):delete r[d]);if(o!==l)for(const d in o)(!t||!q(t,d))&&(delete o[d],h=!0)}h&&qe(e.attrs,"set","")}function yo(e,t,n,s){const[r,o]=e.propsOptions;let i=!1,l;if(t)for(let c in t){if(Dt(c))continue;const h=t[c];let a;r&&q(r,a=ut(c))?!o||!o.includes(a)?n[a]=h:(l||(l={}))[a]=h:Pn(e.emitsOptions,c)||(!(c in s)||h!==s[c])&&(s[c]=h,i=!0)}if(o){const c=W(n),h=l||te;for(let a=0;a<o.length;a++){const d=o[a];n[d]=es(r,c,d,h[d],e,!q(h,d))}}return i}function es(e,t,n,s,r,o){const i=e[n];if(i!=null){const l=q(i,"default");if(l&&s===void 0){const c=i.default;if(i.type!==Function&&!i.skipFactory&&N(c)){const{propsDefaults:h}=r;if(n in h)s=h[n];else{const a=on(r);s=h[n]=c.call(null,t),a()}}else s=c;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!l?s=!1:i[1]&&(s===""||s===_t(n))&&(s=!0))}return s}const ul=new WeakMap;function bo(e,t,n=!1){const s=n?ul:t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},l=[];let c=!1;if(!N(e)){const a=d=>{c=!0;const[g,m]=bo(d,t,!0);fe(i,g),m&&l.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!o&&!c)return se(e)&&s.set(e,Rt),Rt;if(F(o))for(let a=0;a<o.length;a++){const d=ut(o[a]);Vs(d)&&(i[d]=te)}else if(o)for(const a in o){const d=ut(a);if(Vs(d)){const g=o[a],m=i[d]=F(g)||N(g)?{type:g}:fe({},g),A=m.type;let O=!1,V=!0;if(F(A))for(let $=0;$<A.length;++$){const I=A[$],L=N(I)&&I.name;if(L==="Boolean"){O=!0;break}else L==="String"&&(V=!1)}else O=N(A)&&A.name==="Boolean";m[0]=O,m[1]=V,(O||q(m,"default"))&&l.push(d)}}const h=[i,l];return se(e)&&s.set(e,h),h}function Vs(e){return e[0]!=="$"&&!Dt(e)}const Es=e=>e[0]==="_"||e==="$stable",Ss=e=>F(e)?e.map(Ne):[Ne(e)],fl=(e,t,n)=>{if(t._n)return t;const s=ie((...r)=>Ss(t(...r)),n);return s._c=!1,s},wo=(e,t,n)=>{const s=e._ctx;for(const r in e){if(Es(r))continue;const o=e[r];if(N(o))t[r]=fl(r,o,s);else if(o!=null){const i=Ss(o);t[r]=()=>i}}},xo=(e,t)=>{const n=Ss(t);e.slots.default=()=>n},Eo=(e,t,n)=>{for(const s in t)(n||!Es(s))&&(e[s]=t[s])},al=(e,t,n)=>{const s=e.slots=vo();if(e.vnode.shapeFlag&32){const r=t.__;r&&Kn(s,"__",r,!0);const o=t._;o?(Eo(s,t,n),n&&Kn(s,"_",o,!0)):wo(t,s)}else t&&xo(e,t)},dl=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=te;if(s.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:Eo(r,t,n):(o=!t.$stable,wo(t,r)),i=t}else t&&(xo(e,t),i={default:1});if(o)for(const l in r)!Es(l)&&i[l]==null&&delete r[l]},xe=Cl;function hl(e){return pl(e)}function pl(e,t){const n=En();n.__VUE__=!0;const{insert:s,remove:r,patchProp:o,createElement:i,createText:l,createComment:c,setText:h,setElementText:a,parentNode:d,nextSibling:g,setScopeId:m=Ve,insertStaticContent:A}=e,O=(u,f,p,v=null,b=null,y=null,S=void 0,E=null,x=!!f.dynamicChildren)=>{if(u===f)return;u&&!jt(u,f)&&(v=_(u),ge(u,b,y,!0),u=null),f.patchFlag===-2&&(x=!1,f.dynamicChildren=null);const{type:w,ref:H,shapeFlag:C}=f;switch(w){case An:V(u,f,p,v);break;case vt:$(u,f,p,v);break;case Nn:u==null&&I(f,p,v,S);break;case ye:at(u,f,p,v,b,y,S,E,x);break;default:C&1?J(u,f,p,v,b,y,S,E,x):C&6?Ie(u,f,p,v,b,y,S,E,x):(C&64||C&128)&&w.process(u,f,p,v,b,y,S,E,x,T)}H!=null&&b?Kt(H,u&&u.ref,y,f||u,!f):H==null&&u&&u.ref!=null&&Kt(u.ref,null,y,u,!0)},V=(u,f,p,v)=>{if(u==null)s(f.el=l(f.children),p,v);else{const b=f.el=u.el;f.children!==u.children&&h(b,f.children)}},$=(u,f,p,v)=>{u==null?s(f.el=c(f.children||""),p,v):f.el=u.el},I=(u,f,p,v)=>{[u.el,u.anchor]=A(u.children,f,p,v,u.el,u.anchor)},L=({el:u,anchor:f},p,v)=>{let b;for(;u&&u!==f;)b=g(u),s(u,p,v),u=b;s(f,p,v)},M=({el:u,anchor:f})=>{let p;for(;u&&u!==f;)p=g(u),r(u),u=p;r(f)},J=(u,f,p,v,b,y,S,E,x)=>{f.type==="svg"?S="svg":f.type==="math"&&(S="mathml"),u==null?le(f,p,v,b,y,S,E,x):et(u,f,b,y,S,E,x)},le=(u,f,p,v,b,y,S,E)=>{let x,w;const{props:H,shapeFlag:C,transition:z,dirs:j}=u;if(x=u.el=i(u.type,y,H&&H.is,H),C&8?a(x,u.children):C&16&&Me(u.children,x,null,v,b,Fn(u,y),S,E),j&&dt(u,null,v,"created"),ne(x,u,u.scopeId,S,v),H){for(const Z in H)Z!=="value"&&!Dt(Z)&&o(x,Z,null,H[Z],y,v);"value"in H&&o(x,"value",null,H.value,y),(w=H.onVnodeBeforeMount)&&je(w,v,u)}j&&dt(u,null,v,"beforeMount");const B=gl(b,z);B&&z.beforeEnter(x),s(x,f,p),((w=H&&H.onVnodeMounted)||B||j)&&xe(()=>{w&&je(w,v,u),B&&z.enter(x),j&&dt(u,null,v,"mounted")},b)},ne=(u,f,p,v,b)=>{if(p&&m(u,p),v)for(let y=0;y<v.length;y++)m(u,v[y]);if(b){let y=b.subTree;if(f===y||Oo(y.type)&&(y.ssContent===f||y.ssFallback===f)){const S=b.vnode;ne(u,S,S.scopeId,S.slotScopeIds,b.parent)}}},Me=(u,f,p,v,b,y,S,E,x=0)=>{for(let w=x;w<u.length;w++){const H=u[w]=E?ot(u[w]):Ne(u[w]);O(null,H,f,p,v,b,y,S,E)}},et=(u,f,p,v,b,y,S)=>{const E=f.el=u.el;let{patchFlag:x,dynamicChildren:w,dirs:H}=f;x|=u.patchFlag&16;const C=u.props||te,z=f.props||te;let j;if(p&&ht(p,!1),(j=z.onVnodeBeforeUpdate)&&je(j,p,f,u),H&&dt(f,u,p,"beforeUpdate"),p&&ht(p,!0),(C.innerHTML&&z.innerHTML==null||C.textContent&&z.textContent==null)&&a(E,""),w?Te(u.dynamicChildren,w,E,p,v,Fn(f,b),y):S||U(u,f,E,null,p,v,Fn(f,b),y,!1),x>0){if(x&16)tt(E,C,z,p,b);else if(x&2&&C.class!==z.class&&o(E,"class",null,z.class,b),x&4&&o(E,"style",C.style,z.style,b),x&8){const B=f.dynamicProps;for(let Z=0;Z<B.length;Z++){const G=B[Z],me=C[G],ve=z[G];(ve!==me||G==="value")&&o(E,G,me,ve,b,p)}}x&1&&u.children!==f.children&&a(E,f.children)}else!S&&w==null&&tt(E,C,z,p,b);((j=z.onVnodeUpdated)||H)&&xe(()=>{j&&je(j,p,f,u),H&&dt(f,u,p,"updated")},v)},Te=(u,f,p,v,b,y,S)=>{for(let E=0;E<f.length;E++){const x=u[E],w=f[E],H=x.el&&(x.type===ye||!jt(x,w)||x.shapeFlag&198)?d(x.el):p;O(x,w,H,null,v,b,y,S,!0)}},tt=(u,f,p,v,b)=>{if(f!==p){if(f!==te)for(const y in f)!Dt(y)&&!(y in p)&&o(u,y,f[y],null,b,v);for(const y in p){if(Dt(y))continue;const S=p[y],E=f[y];S!==E&&y!=="value"&&o(u,y,E,S,b,v)}"value"in p&&o(u,"value",f.value,p.value,b)}},at=(u,f,p,v,b,y,S,E,x)=>{const w=f.el=u?u.el:l(""),H=f.anchor=u?u.anchor:l("");let{patchFlag:C,dynamicChildren:z,slotScopeIds:j}=f;j&&(E=E?E.concat(j):j),u==null?(s(w,p,v),s(H,p,v),Me(f.children||[],p,H,b,y,S,E,x)):C>0&&C&64&&z&&u.dynamicChildren?(Te(u.dynamicChildren,z,p,b,y,S,E),(f.key!=null||b&&f===b.subTree)&&So(u,f,!0)):U(u,f,p,H,b,y,S,E,x)},Ie=(u,f,p,v,b,y,S,E,x)=>{f.slotScopeIds=E,u==null?f.shapeFlag&512?b.ctx.activate(f,p,v,S,x):Ht(f,p,v,b,y,S,x):yt(u,f,x)},Ht=(u,f,p,v,b,y,S)=>{const E=u.component=zl(u,v,b);if(co(u)&&(E.ctx.renderer=T),Hl(E,!1,S),E.asyncDep){if(b&&b.registerDep(E,oe,S),!u.el){const x=E.subTree=Q(vt);$(null,x,f,p)}}else oe(E,u,f,p,b,y,S)},yt=(u,f,p)=>{const v=f.component=u.component;if(Sl(u,f,p))if(v.asyncDep&&!v.asyncResolved){Y(v,f,p);return}else v.next=f,v.update();else f.el=u.el,v.vnode=f},oe=(u,f,p,v,b,y,S)=>{const E=()=>{if(u.isMounted){let{next:C,bu:z,u:j,parent:B,vnode:Z}=u;{const $e=Ro(u);if($e){C&&(C.el=Z.el,Y(u,C,S)),$e.asyncDep.then(()=>{u.isUnmounted||E()});return}}let G=C,me;ht(u,!1),C?(C.el=Z.el,Y(u,C,S)):C=Z,z&&Tn(z),(me=C.props&&C.props.onVnodeBeforeUpdate)&&je(me,B,C,Z),ht(u,!0);const ve=ks(u),He=u.subTree;u.subTree=ve,O(He,ve,d(He.el),_(He),u,b,y),C.el=ve.el,G===null&&Rl(u,ve.el),j&&xe(j,b),(me=C.props&&C.props.onVnodeUpdated)&&xe(()=>je(me,B,C,Z),b)}else{let C;const{el:z,props:j}=f,{bm:B,m:Z,parent:G,root:me,type:ve}=u,He=At(f);ht(u,!1),B&&Tn(B),!He&&(C=j&&j.onVnodeBeforeMount)&&je(C,G,f),ht(u,!0);{me.ce&&me.ce._def.shadowRoot!==!1&&me.ce._injectChildStyle(ve);const $e=u.subTree=ks(u);O(null,$e,p,v,u,b,y),f.el=$e.el}if(Z&&xe(Z,b),!He&&(C=j&&j.onVnodeMounted)){const $e=f;xe(()=>je(C,G,$e),b)}(f.shapeFlag&256||G&&At(G.vnode)&&G.vnode.shapeFlag&256)&&u.a&&xe(u.a,b),u.isMounted=!0,f=p=v=null}};u.scope.on();const x=u.effect=new $r(E);u.scope.off();const w=u.update=x.run.bind(x),H=u.job=x.runIfDirty.bind(x);H.i=u,H.id=u.uid,x.scheduler=()=>ws(H),ht(u,!0),w()},Y=(u,f,p)=>{f.component=u;const v=u.vnode.props;u.vnode=f,u.next=null,cl(u,f.props,v,p),dl(u,f.children,p),Qe(),$s(u),Je()},U=(u,f,p,v,b,y,S,E,x=!1)=>{const w=u&&u.children,H=u?u.shapeFlag:0,C=f.children,{patchFlag:z,shapeFlag:j}=f;if(z>0){if(z&128){nt(w,C,p,v,b,y,S,E,x);return}else if(z&256){Be(w,C,p,v,b,y,S,E,x);return}}j&8?(H&16&&Se(w,b,y),C!==w&&a(p,C)):H&16?j&16?nt(w,C,p,v,b,y,S,E,x):Se(w,b,y,!0):(H&8&&a(p,""),j&16&&Me(C,p,v,b,y,S,E,x))},Be=(u,f,p,v,b,y,S,E,x)=>{u=u||Rt,f=f||Rt;const w=u.length,H=f.length,C=Math.min(w,H);let z;for(z=0;z<C;z++){const j=f[z]=x?ot(f[z]):Ne(f[z]);O(u[z],j,p,null,b,y,S,E,x)}w>H?Se(u,b,y,!0,!1,C):Me(f,p,v,b,y,S,E,x,C)},nt=(u,f,p,v,b,y,S,E,x)=>{let w=0;const H=f.length;let C=u.length-1,z=H-1;for(;w<=C&&w<=z;){const j=u[w],B=f[w]=x?ot(f[w]):Ne(f[w]);if(jt(j,B))O(j,B,p,null,b,y,S,E,x);else break;w++}for(;w<=C&&w<=z;){const j=u[C],B=f[z]=x?ot(f[z]):Ne(f[z]);if(jt(j,B))O(j,B,p,null,b,y,S,E,x);else break;C--,z--}if(w>C){if(w<=z){const j=z+1,B=j<H?f[j].el:v;for(;w<=z;)O(null,f[w]=x?ot(f[w]):Ne(f[w]),p,B,b,y,S,E,x),w++}}else if(w>z)for(;w<=C;)ge(u[w],b,y,!0),w++;else{const j=w,B=w,Z=new Map;for(w=B;w<=z;w++){const we=f[w]=x?ot(f[w]):Ne(f[w]);we.key!=null&&Z.set(we.key,w)}let G,me=0;const ve=z-B+1;let He=!1,$e=0;const $t=new Array(ve);for(w=0;w<ve;w++)$t[w]=0;for(w=j;w<=C;w++){const we=u[w];if(me>=ve){ge(we,b,y,!0);continue}let Le;if(we.key!=null)Le=Z.get(we.key);else for(G=B;G<=z;G++)if($t[G-B]===0&&jt(we,f[G])){Le=G;break}Le===void 0?ge(we,b,y,!0):($t[Le-B]=w+1,Le>=$e?$e=Le:He=!0,O(we,f[Le],p,null,b,y,S,E,x),me++)}const Ms=He?ml($t):Rt;for(G=Ms.length-1,w=ve-1;w>=0;w--){const we=B+w,Le=f[we],Ts=we+1<H?f[we+1].el:v;$t[w]===0?O(null,Le,p,Ts,b,y,S,E,x):He&&(G<0||w!==Ms[G]?ze(Le,p,Ts,2):G--)}}},ze=(u,f,p,v,b=null)=>{const{el:y,type:S,transition:E,children:x,shapeFlag:w}=u;if(w&6){ze(u.component.subTree,f,p,v);return}if(w&128){u.suspense.move(f,p,v);return}if(w&64){S.move(u,f,p,T);return}if(S===ye){s(y,f,p);for(let C=0;C<x.length;C++)ze(x[C],f,p,v);s(u.anchor,f,p);return}if(S===Nn){L(u,f,p);return}if(v!==2&&w&1&&E)if(v===0)E.beforeEnter(y),s(y,f,p),xe(()=>E.enter(y),b);else{const{leave:C,delayLeave:z,afterLeave:j}=E,B=()=>{u.ctx.isUnmounted?r(y):s(y,f,p)},Z=()=>{C(y,()=>{B(),j&&j()})};z?z(y,B,Z):Z()}else s(y,f,p)},ge=(u,f,p,v=!1,b=!1)=>{const{type:y,props:S,ref:E,children:x,dynamicChildren:w,shapeFlag:H,patchFlag:C,dirs:z,cacheIndex:j}=u;if(C===-2&&(b=!1),E!=null&&(Qe(),Kt(E,null,p,u,!0),Je()),j!=null&&(f.renderCache[j]=void 0),H&256){f.ctx.deactivate(u);return}const B=H&1&&z,Z=!At(u);let G;if(Z&&(G=S&&S.onVnodeBeforeUnmount)&&je(G,f,u),H&6)ln(u.component,p,v);else{if(H&128){u.suspense.unmount(p,v);return}B&&dt(u,null,f,"beforeUnmount"),H&64?u.type.remove(u,f,p,T,v):w&&!w.hasOnce&&(y!==ye||C>0&&C&64)?Se(w,f,p,!1,!0):(y===ye&&C&384||!b&&H&16)&&Se(x,f,p),v&&bt(u)}(Z&&(G=S&&S.onVnodeUnmounted)||B)&&xe(()=>{G&&je(G,f,u),B&&dt(u,null,f,"unmounted")},p)},bt=u=>{const{type:f,el:p,anchor:v,transition:b}=u;if(f===ye){wt(p,v);return}if(f===Nn){M(u);return}const y=()=>{r(p),b&&!b.persisted&&b.afterLeave&&b.afterLeave()};if(u.shapeFlag&1&&b&&!b.persisted){const{leave:S,delayLeave:E}=b,x=()=>S(p,y);E?E(u.el,y,x):x()}else y()},wt=(u,f)=>{let p;for(;u!==f;)p=g(u),r(u),u=p;r(f)},ln=(u,f,p)=>{const{bum:v,scope:b,job:y,subTree:S,um:E,m:x,a:w,parent:H,slots:{__:C}}=u;Ds(x),Ds(w),v&&Tn(v),H&&F(C)&&C.forEach(z=>{H.renderCache[z]=void 0}),b.stop(),y&&(y.flags|=8,ge(S,u,f,p)),E&&xe(E,f),xe(()=>{u.isUnmounted=!0},f),f&&f.pendingBranch&&!f.isUnmounted&&u.asyncDep&&!u.asyncResolved&&u.suspenseId===f.pendingId&&(f.deps--,f.deps===0&&f.resolve())},Se=(u,f,p,v=!1,b=!1,y=0)=>{for(let S=y;S<u.length;S++)ge(u[S],f,p,v,b)},_=u=>{if(u.shapeFlag&6)return _(u.component.subTree);if(u.shapeFlag&128)return u.suspense.next();const f=g(u.anchor||u.el),p=f&&f[Fi];return p?g(p):f};let P=!1;const R=(u,f,p)=>{u==null?f._vnode&&ge(f._vnode,null,null,!0):O(f._vnode||null,u,f,null,null,null,p),f._vnode=u,P||(P=!0,$s(),ro(),P=!1)},T={p:O,um:ge,m:ze,r:bt,mt:Ht,mc:Me,pc:U,pbc:Te,n:_,o:e};return{render:R,hydrate:void 0,createApp:il(R)}}function Fn({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function ht({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function gl(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function So(e,t,n=!1){const s=e.children,r=t.children;if(F(s)&&F(r))for(let o=0;o<s.length;o++){const i=s[o];let l=r[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[o]=ot(r[o]),l.el=i.el),!n&&l.patchFlag!==-2&&So(i,l)),l.type===An&&(l.el=i.el),l.type===vt&&!l.el&&(l.el=i.el)}}function ml(e){const t=e.slice(),n=[0];let s,r,o,i,l;const c=e.length;for(s=0;s<c;s++){const h=e[s];if(h!==0){if(r=n[n.length-1],e[r]<h){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<h?o=l+1:i=l;h<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function Ro(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Ro(t)}function Ds(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const vl=Symbol.for("v-scx"),_l=()=>Ye(vl);function dn(e,t,n){return Co(e,t,n)}function Co(e,t,n=te){const{immediate:s,deep:r,flush:o,once:i}=n,l=fe({},n),c=t&&s||!t&&o!=="post";let h;if(tn){if(o==="sync"){const m=_l();h=m.__watcherHandles||(m.__watcherHandles=[])}else if(!c){const m=()=>{};return m.stop=Ve,m.resume=Ve,m.pause=Ve,m}}const a=pe;l.call=(m,A,O)=>De(m,a,A,O);let d=!1;o==="post"?l.scheduler=m=>{xe(m,a&&a.suspense)}:o!=="sync"&&(d=!0,l.scheduler=(m,A)=>{A?m():ws(m)}),l.augmentJob=m=>{t&&(m.flags|=4),d&&(m.flags|=2,a&&(m.id=a.uid,m.i=a))};const g=Hi(e,t,l);return tn&&(h?h.push(g):c&&g()),g}function yl(e,t,n){const s=this.proxy,r=re(e)?e.includes(".")?Po(s,e):()=>s[e]:e.bind(s,s);let o;N(t)?o=t:(o=t.handler,n=t);const i=on(this),l=Co(r,o.bind(s),n);return i(),l}function Po(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const bl=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${ut(t)}Modifiers`]||e[`${_t(t)}Modifiers`];function wl(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||te;let r=n;const o=t.startsWith("update:"),i=o&&bl(s,t.slice(7));i&&(i.trim&&(r=n.map(a=>re(a)?a.trim():a)),i.number&&(r=n.map(ti)));let l,c=s[l=Mn(t)]||s[l=Mn(ut(t))];!c&&o&&(c=s[l=Mn(_t(t))]),c&&De(c,e,6,r);const h=s[l+"Once"];if(h){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,De(h,e,6,r)}}function Ao(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const o=e.emits;let i={},l=!1;if(!N(e)){const c=h=>{const a=Ao(h,t,!0);a&&(l=!0,fe(i,a))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!o&&!l?(se(e)&&s.set(e,null),null):(F(o)?o.forEach(c=>i[c]=null):fe(i,o),se(e)&&s.set(e,i),i)}function Pn(e,t){return!e||!bn(t)?!1:(t=t.slice(2).replace(/Once$/,""),q(e,t[0].toLowerCase()+t.slice(1))||q(e,_t(t))||q(e,t))}function ks(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:l,emit:c,render:h,renderCache:a,props:d,data:g,setupState:m,ctx:A,inheritAttrs:O}=e,V=vn(e);let $,I;try{if(n.shapeFlag&4){const M=r||s,J=M;$=Ne(h.call(J,M,a,d,m,g,A)),I=l}else{const M=t;$=Ne(M.length>1?M(d,{attrs:l,slots:i,emit:c}):M(d,null)),I=t.props?l:xl(l)}}catch(M){qt.length=0,Rn(M,e,1),$=Q(vt)}let L=$;if(I&&O!==!1){const M=Object.keys(I),{shapeFlag:J}=L;M.length&&J&7&&(o&&M.some(cs)&&(I=El(I,o)),L=Mt(L,I,!1,!0))}return n.dirs&&(L=Mt(L,null,!1,!0),L.dirs=L.dirs?L.dirs.concat(n.dirs):n.dirs),n.transition&&xs(L,n.transition),$=L,vn(V),$}const xl=e=>{let t;for(const n in e)(n==="class"||n==="style"||bn(n))&&((t||(t={}))[n]=e[n]);return t},El=(e,t)=>{const n={};for(const s in e)(!cs(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function Sl(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:l,patchFlag:c}=t,h=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?Bs(s,i,h):!!i;if(c&8){const a=t.dynamicProps;for(let d=0;d<a.length;d++){const g=a[d];if(i[g]!==s[g]&&!Pn(h,g))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===i?!1:s?i?Bs(s,i,h):!0:!!i;return!1}function Bs(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!Pn(n,o))return!0}return!1}function Rl({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Oo=e=>e.__isSuspense;function Cl(e,t){t&&t.pendingBranch?F(e)?t.effects.push(...e):t.effects.push(e):ji(e)}const ye=Symbol.for("v-fgt"),An=Symbol.for("v-txt"),vt=Symbol.for("v-cmt"),Nn=Symbol.for("v-stc"),qt=[];let Ee=null;function Re(e=!1){qt.push(Ee=e?null:[])}function Pl(){qt.pop(),Ee=qt[qt.length-1]||null}let Zt=1;function Us(e,t=!1){Zt+=e,e<0&&Ee&&t&&(Ee.hasOnce=!0)}function Mo(e){return e.dynamicChildren=Zt>0?Ee||Rt:null,Pl(),Zt>0&&Ee&&Ee.push(e),e}function ke(e,t,n,s,r,o){return Mo(D(e,t,n,s,r,o,!0))}function Ks(e,t,n,s,r){return Mo(Q(e,t,n,s,r,!0))}function en(e){return e?e.__v_isVNode===!0:!1}function jt(e,t){return e.type===t.type&&e.key===t.key}const To=({key:e})=>e??null,hn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?re(e)||ue(e)||N(e)?{i:be,r:e,k:t,f:!!n}:e:null);function D(e,t=null,n=null,s=0,r=null,o=e===ye?0:1,i=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&To(t),ref:t&&hn(t),scopeId:io,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:be};return l?(Rs(c,n),o&128&&e.normalize(c)):n&&(c.shapeFlag|=re(n)?8:16),Zt>0&&!i&&Ee&&(c.patchFlag>0||o&6)&&c.patchFlag!==32&&Ee.push(c),c}const Q=Al;function Al(e,t=null,n=null,s=0,r=null,o=!1){if((!e||e===Xi)&&(e=vt),en(e)){const l=Mt(e,t,!0);return n&&Rs(l,n),Zt>0&&!o&&Ee&&(l.shapeFlag&6?Ee[Ee.indexOf(e)]=l:Ee.push(l)),l.patchFlag=-2,l}if(Fl(e)&&(e=e.__vccOpts),t){t=Ol(t);let{class:l,style:c}=t;l&&!re(l)&&(t.class=ds(l)),se(c)&&(bs(c)&&!F(c)&&(c=fe({},c)),t.style=as(c))}const i=re(e)?1:Oo(e)?128:Ni(e)?64:se(e)?4:N(e)?2:0;return D(e,t,n,s,r,i,o,!0)}function Ol(e){return e?bs(e)||_o(e)?fe({},e):e:null}function Mt(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:l,transition:c}=e,h=t?Ml(r||{},t):r,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:h,key:h&&To(h),ref:t&&t.ref?n&&o?F(o)?o.concat(hn(t)):[o,hn(t)]:hn(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ye?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Mt(e.ssContent),ssFallback:e.ssFallback&&Mt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&xs(a,c.clone(a)),a}function k(e=" ",t=0){return Q(An,null,e,t)}function Ne(e){return e==null||typeof e=="boolean"?Q(vt):F(e)?Q(ye,null,e.slice()):en(e)?ot(e):Q(An,null,String(e))}function ot(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Mt(e)}function Rs(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(F(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),Rs(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!_o(t)?t._ctx=be:r===3&&be&&(be.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else N(t)?(t={default:t,_ctx:be},n=32):(t=String(t),s&64?(n=16,t=[k(t)]):n=8);e.children=t,e.shapeFlag|=n}function Ml(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=ds([t.class,s.class]));else if(r==="style")t.style=as([t.style,s.style]);else if(bn(r)){const o=t[r],i=s[r];i&&o!==i&&!(F(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=s[r])}return t}function je(e,t,n,s=null){De(e,t,7,[n,s])}const Tl=go();let Il=0;function zl(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||Tl,o={uid:Il++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Hr(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:bo(s,r),emitsOptions:Ao(s,r),emit:null,emitted:null,propsDefaults:te,inheritAttrs:s.inheritAttrs,ctx:te,data:te,props:te,attrs:te,slots:te,refs:te,setupState:te,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=wl.bind(null,o),e.ce&&e.ce(o),o}let pe=null,yn,ts;{const e=En(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};yn=t("__VUE_INSTANCE_SETTERS__",n=>pe=n),ts=t("__VUE_SSR_SETTERS__",n=>tn=n)}const on=e=>{const t=pe;return yn(e),e.scope.on(),()=>{e.scope.off(),yn(t)}},Ws=()=>{pe&&pe.scope.off(),yn(null)};function Io(e){return e.vnode.shapeFlag&4}let tn=!1;function Hl(e,t=!1,n=!1){t&&ts(t);const{props:s,children:r}=e.vnode,o=Io(e);ll(e,s,o,t),al(e,r,n||t);const i=o?$l(e,t):void 0;return t&&ts(!1),i}function $l(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Zi);const{setup:s}=n;if(s){Qe();const r=e.setupContext=s.length>1?jl(e):null,o=on(e),i=rn(s,e,0,[e.props,r]),l=Cr(i);if(Je(),o(),(l||e.sp)&&!At(e)&&lo(e),l){if(i.then(Ws,Ws),t)return i.then(c=>{qs(e,c)}).catch(c=>{Rn(c,e,0)});e.asyncDep=i}else qs(e,i)}else zo(e)}function qs(e,t,n){N(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:se(t)&&(e.setupState=eo(t)),zo(e)}function zo(e,t,n){const s=e.type;e.render||(e.render=s.render||Ve);{const r=on(e);Qe();try{el(e)}finally{Je(),r()}}}const Ll={get(e,t){return ce(e,"get",""),e[t]}};function jl(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Ll),slots:e.slots,emit:e.emit,expose:t}}function Cs(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(eo(Jr(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Wt)return Wt[n](e)},has(t,n){return n in t||n in Wt}})):e.proxy}function Fl(e){return N(e)&&"__vccOpts"in e}const Ce=(e,t)=>Ii(e,t,tn);function Ho(e,t,n){const s=arguments.length;return s===2?se(t)&&!F(t)?en(t)?Q(e,null,[t]):Q(e,t):Q(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&en(n)&&(n=[n]),Q(e,t,n))}const Nl="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ns;const Gs=typeof window<"u"&&window.trustedTypes;if(Gs)try{ns=Gs.createPolicy("vue",{createHTML:e=>e})}catch{}const $o=ns?e=>ns.createHTML(e):e=>e,Vl="http://www.w3.org/2000/svg",Dl="http://www.w3.org/1998/Math/MathML",We=typeof document<"u"?document:null,Ys=We&&We.createElement("template"),kl={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?We.createElementNS(Vl,e):t==="mathml"?We.createElementNS(Dl,e):n?We.createElement(e,{is:n}):We.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>We.createTextNode(e),createComment:e=>We.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>We.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===o||!(r=r.nextSibling)););else{Ys.innerHTML=$o(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=Ys.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Bl=Symbol("_vtc");function Ul(e,t,n){const s=e[Bl];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Qs=Symbol("_vod"),Kl=Symbol("_vsh"),Wl=Symbol(""),ql=/(^|;)\s*display\s*:/;function Gl(e,t,n){const s=e.style,r=re(n);let o=!1;if(n&&!r){if(t)if(re(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&pn(s,l,"")}else for(const i in t)n[i]==null&&pn(s,i,"");for(const i in n)i==="display"&&(o=!0),pn(s,i,n[i])}else if(r){if(t!==n){const i=s[Wl];i&&(n+=";"+i),s.cssText=n,o=ql.test(n)}}else t&&e.removeAttribute("style");Qs in e&&(e[Qs]=o?s.display:"",e[Kl]&&(s.display="none"))}const Js=/\s*!important$/;function pn(e,t,n){if(F(n))n.forEach(s=>pn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Yl(e,t);Js.test(n)?e.setProperty(_t(s),n.replace(Js,""),"important"):e[s]=n}}const Xs=["Webkit","Moz","ms"],Vn={};function Yl(e,t){const n=Vn[t];if(n)return n;let s=ut(t);if(s!=="filter"&&s in e)return Vn[t]=s;s=Or(s);for(let r=0;r<Xs.length;r++){const o=Xs[r]+s;if(o in e)return Vn[t]=o}return t}const Zs="http://www.w3.org/1999/xlink";function er(e,t,n,s,r,o=li(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Zs,t.slice(6,t.length)):e.setAttributeNS(Zs,t,n):n==null||o&&!Mr(n)?e.removeAttribute(t):e.setAttribute(t,o?"":Xe(n)?String(n):n)}function tr(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?$o(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Mr(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function Ql(e,t,n,s){e.addEventListener(t,n,s)}function Jl(e,t,n,s){e.removeEventListener(t,n,s)}const nr=Symbol("_vei");function Xl(e,t,n,s,r=null){const o=e[nr]||(e[nr]={}),i=o[t];if(s&&i)i.value=s;else{const[l,c]=Zl(t);if(s){const h=o[t]=nc(s,r);Ql(e,l,h,c)}else i&&(Jl(e,l,i,c),o[t]=void 0)}}const sr=/(?:Once|Passive|Capture)$/;function Zl(e){let t;if(sr.test(e)){t={};let s;for(;s=e.match(sr);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):_t(e.slice(2)),t]}let Dn=0;const ec=Promise.resolve(),tc=()=>Dn||(ec.then(()=>Dn=0),Dn=Date.now());function nc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;De(sc(s,n.value),t,5,[s])};return n.value=e,n.attached=tc(),n}function sc(e,t){if(F(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const rr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,rc=(e,t,n,s,r,o)=>{const i=r==="svg";t==="class"?Ul(e,s,i):t==="style"?Gl(e,n,s):bn(t)?cs(t)||Xl(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):oc(e,t,s,i))?(tr(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&er(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!re(s))?tr(e,ut(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),er(e,t,s,i))};function oc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&rr(t)&&N(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return rr(t)&&re(n)?!1:t in e}const ic=fe({patchProp:rc},kl);let or;function lc(){return or||(or=hl(ic))}const cc=(...e)=>{const t=lc().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=fc(s);if(!r)return;const o=t._component;!N(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,uc(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function uc(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function fc(e){return re(e)?document.querySelector(e):e}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const ac=Symbol();var ir;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(ir||(ir={}));function dc(){const e=ci(!0),t=e.run(()=>Xr({}));let n=[],s=[];const r=Jr({install(o){r._a=o,o.provide(ac,r),o.config.globalProperties.$pinia=r,s.forEach(i=>n.push(i)),s=[]},use(o){return this._a?n.push(o):s.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}const hc="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%20261.76%20226.69'%3e%3cpath%20d='M161.096.001l-30.225%2052.351L100.647.001H-.005l130.877%20226.688L261.749.001z'%20fill='%2341b883'/%3e%3cpath%20d='M161.096.001l-30.225%2052.351L100.647.001H52.346l78.526%20136.01L209.398.001z'%20fill='%2334495e'/%3e%3c/svg%3e";/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const St=typeof document<"u";function Lo(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function pc(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Lo(e.default)}const K=Object.assign;function kn(e,t){const n={};for(const s in t){const r=t[s];n[s]=Oe(r)?r.map(e):e(r)}return n}const Gt=()=>{},Oe=Array.isArray,jo=/#/g,gc=/&/g,mc=/\//g,vc=/=/g,_c=/\?/g,Fo=/\+/g,yc=/%5B/g,bc=/%5D/g,No=/%5E/g,wc=/%60/g,Vo=/%7B/g,xc=/%7C/g,Do=/%7D/g,Ec=/%20/g;function Ps(e){return encodeURI(""+e).replace(xc,"|").replace(yc,"[").replace(bc,"]")}function Sc(e){return Ps(e).replace(Vo,"{").replace(Do,"}").replace(No,"^")}function ss(e){return Ps(e).replace(Fo,"%2B").replace(Ec,"+").replace(jo,"%23").replace(gc,"%26").replace(wc,"`").replace(Vo,"{").replace(Do,"}").replace(No,"^")}function Rc(e){return ss(e).replace(vc,"%3D")}function Cc(e){return Ps(e).replace(jo,"%23").replace(_c,"%3F")}function Pc(e){return e==null?"":Cc(e).replace(mc,"%2F")}function nn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Ac=/\/$/,Oc=e=>e.replace(Ac,"");function Bn(e,t,n="/"){let s,r={},o="",i="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(s=t.slice(0,c),o=t.slice(c+1,l>-1?l:t.length),r=e(o)),l>-1&&(s=s||t.slice(0,l),i=t.slice(l,t.length)),s=zc(s??t,n),{fullPath:s+(o&&"?")+o+i,path:s,query:r,hash:nn(i)}}function Mc(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function lr(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Tc(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&Tt(t.matched[s],n.matched[r])&&ko(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Tt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function ko(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Ic(e[n],t[n]))return!1;return!0}function Ic(e,t){return Oe(e)?cr(e,t):Oe(t)?cr(t,e):e===t}function cr(e,t){return Oe(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function zc(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let o=n.length-1,i,l;for(i=0;i<s.length;i++)if(l=s[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const st={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var sn;(function(e){e.pop="pop",e.push="push"})(sn||(sn={}));var Yt;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Yt||(Yt={}));function Hc(e){if(!e)if(St){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Oc(e)}const $c=/^[^#]+#/;function Lc(e,t){return e.replace($c,"#")+t}function jc(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const On=()=>({left:window.scrollX,top:window.scrollY});function Fc(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=jc(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function ur(e,t){return(history.state?history.state.position-t:-1)+e}const rs=new Map;function Nc(e,t){rs.set(e,t)}function Vc(e){const t=rs.get(e);return rs.delete(e),t}let Dc=()=>location.protocol+"//"+location.host;function Bo(e,t){const{pathname:n,search:s,hash:r}=t,o=e.indexOf("#");if(o>-1){let l=r.includes(e.slice(o))?e.slice(o).length:1,c=r.slice(l);return c[0]!=="/"&&(c="/"+c),lr(c,"")}return lr(n,e)+s+r}function kc(e,t,n,s){let r=[],o=[],i=null;const l=({state:g})=>{const m=Bo(e,location),A=n.value,O=t.value;let V=0;if(g){if(n.value=m,t.value=g,i&&i===A){i=null;return}V=O?g.position-O.position:0}else s(m);r.forEach($=>{$(n.value,A,{delta:V,type:sn.pop,direction:V?V>0?Yt.forward:Yt.back:Yt.unknown})})};function c(){i=n.value}function h(g){r.push(g);const m=()=>{const A=r.indexOf(g);A>-1&&r.splice(A,1)};return o.push(m),m}function a(){const{history:g}=window;g.state&&g.replaceState(K({},g.state,{scroll:On()}),"")}function d(){for(const g of o)g();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",a)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",a,{passive:!0}),{pauseListeners:c,listen:h,destroy:d}}function fr(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?On():null}}function Bc(e){const{history:t,location:n}=window,s={value:Bo(e,n)},r={value:t.state};r.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(c,h,a){const d=e.indexOf("#"),g=d>-1?(n.host&&document.querySelector("base")?e:e.slice(d))+c:Dc()+e+c;try{t[a?"replaceState":"pushState"](h,"",g),r.value=h}catch(m){console.error(m),n[a?"replace":"assign"](g)}}function i(c,h){const a=K({},t.state,fr(r.value.back,c,r.value.forward,!0),h,{position:r.value.position});o(c,a,!0),s.value=c}function l(c,h){const a=K({},r.value,t.state,{forward:c,scroll:On()});o(a.current,a,!0);const d=K({},fr(s.value,c,null),{position:a.position+1},h);o(c,d,!1),s.value=c}return{location:s,state:r,push:l,replace:i}}function Uc(e){e=Hc(e);const t=Bc(e),n=kc(e,t.state,t.location,t.replace);function s(o,i=!0){i||n.pauseListeners(),history.go(o)}const r=K({location:"",base:e,go:s,createHref:Lc.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Kc(e){return typeof e=="string"||e&&typeof e=="object"}function Uo(e){return typeof e=="string"||typeof e=="symbol"}const Ko=Symbol("");var ar;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(ar||(ar={}));function It(e,t){return K(new Error,{type:e,[Ko]:!0},t)}function Ke(e,t){return e instanceof Error&&Ko in e&&(t==null||!!(e.type&t))}const dr="[^/]+?",Wc={sensitive:!1,strict:!1,start:!0,end:!0},qc=/[.+*?^${}()[\]/\\]/g;function Gc(e,t){const n=K({},Wc,t),s=[];let r=n.start?"^":"";const o=[];for(const h of e){const a=h.length?[]:[90];n.strict&&!h.length&&(r+="/");for(let d=0;d<h.length;d++){const g=h[d];let m=40+(n.sensitive?.25:0);if(g.type===0)d||(r+="/"),r+=g.value.replace(qc,"\\$&"),m+=40;else if(g.type===1){const{value:A,repeatable:O,optional:V,regexp:$}=g;o.push({name:A,repeatable:O,optional:V});const I=$||dr;if(I!==dr){m+=10;try{new RegExp(`(${I})`)}catch(M){throw new Error(`Invalid custom RegExp for param "${A}" (${I}): `+M.message)}}let L=O?`((?:${I})(?:/(?:${I}))*)`:`(${I})`;d||(L=V&&h.length<2?`(?:/${L})`:"/"+L),V&&(L+="?"),r+=L,m+=20,V&&(m+=-8),O&&(m+=-20),I===".*"&&(m+=-50)}a.push(m)}s.push(a)}if(n.strict&&n.end){const h=s.length-1;s[h][s[h].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function l(h){const a=h.match(i),d={};if(!a)return null;for(let g=1;g<a.length;g++){const m=a[g]||"",A=o[g-1];d[A.name]=m&&A.repeatable?m.split("/"):m}return d}function c(h){let a="",d=!1;for(const g of e){(!d||!a.endsWith("/"))&&(a+="/"),d=!1;for(const m of g)if(m.type===0)a+=m.value;else if(m.type===1){const{value:A,repeatable:O,optional:V}=m,$=A in h?h[A]:"";if(Oe($)&&!O)throw new Error(`Provided param "${A}" is an array but it is not repeatable (* or + modifiers)`);const I=Oe($)?$.join("/"):$;if(!I)if(V)g.length<2&&(a.endsWith("/")?a=a.slice(0,-1):d=!0);else throw new Error(`Missing required param "${A}"`);a+=I}}return a||"/"}return{re:i,score:s,keys:o,parse:l,stringify:c}}function Yc(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Wo(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const o=Yc(s[n],r[n]);if(o)return o;n++}if(Math.abs(r.length-s.length)===1){if(hr(s))return 1;if(hr(r))return-1}return r.length-s.length}function hr(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Qc={type:0,value:""},Jc=/[a-zA-Z0-9_]/;function Xc(e){if(!e)return[[]];if(e==="/")return[[Qc]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${h}": ${m}`)}let n=0,s=n;const r=[];let o;function i(){o&&r.push(o),o=[]}let l=0,c,h="",a="";function d(){h&&(n===0?o.push({type:0,value:h}):n===1||n===2||n===3?(o.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${h}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:h,regexp:a,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),h="")}function g(){h+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:c==="/"?(h&&d(),i()):c===":"?(d(),n=1):g();break;case 4:g(),n=s;break;case 1:c==="("?n=2:Jc.test(c)?g():(d(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?a[a.length-1]=="\\"?a=a.slice(0,-1)+c:n=3:a+=c;break;case 3:d(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,a="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${h}"`),d(),i(),r}function Zc(e,t,n){const s=Gc(Xc(e.path),n),r=K(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function eu(e,t){const n=[],s=new Map;t=vr({strict:!1,end:!0,sensitive:!1},t);function r(d){return s.get(d)}function o(d,g,m){const A=!m,O=gr(d);O.aliasOf=m&&m.record;const V=vr(t,d),$=[O];if("alias"in d){const M=typeof d.alias=="string"?[d.alias]:d.alias;for(const J of M)$.push(gr(K({},O,{components:m?m.record.components:O.components,path:J,aliasOf:m?m.record:O})))}let I,L;for(const M of $){const{path:J}=M;if(g&&J[0]!=="/"){const le=g.record.path,ne=le[le.length-1]==="/"?"":"/";M.path=g.record.path+(J&&ne+J)}if(I=Zc(M,g,V),m?m.alias.push(I):(L=L||I,L!==I&&L.alias.push(I),A&&d.name&&!mr(I)&&i(d.name)),qo(I)&&c(I),O.children){const le=O.children;for(let ne=0;ne<le.length;ne++)o(le[ne],I,m&&m.children[ne])}m=m||I}return L?()=>{i(L)}:Gt}function i(d){if(Uo(d)){const g=s.get(d);g&&(s.delete(d),n.splice(n.indexOf(g),1),g.children.forEach(i),g.alias.forEach(i))}else{const g=n.indexOf(d);g>-1&&(n.splice(g,1),d.record.name&&s.delete(d.record.name),d.children.forEach(i),d.alias.forEach(i))}}function l(){return n}function c(d){const g=su(d,n);n.splice(g,0,d),d.record.name&&!mr(d)&&s.set(d.record.name,d)}function h(d,g){let m,A={},O,V;if("name"in d&&d.name){if(m=s.get(d.name),!m)throw It(1,{location:d});V=m.record.name,A=K(pr(g.params,m.keys.filter(L=>!L.optional).concat(m.parent?m.parent.keys.filter(L=>L.optional):[]).map(L=>L.name)),d.params&&pr(d.params,m.keys.map(L=>L.name))),O=m.stringify(A)}else if(d.path!=null)O=d.path,m=n.find(L=>L.re.test(O)),m&&(A=m.parse(O),V=m.record.name);else{if(m=g.name?s.get(g.name):n.find(L=>L.re.test(g.path)),!m)throw It(1,{location:d,currentLocation:g});V=m.record.name,A=K({},g.params,d.params),O=m.stringify(A)}const $=[];let I=m;for(;I;)$.unshift(I.record),I=I.parent;return{name:V,path:O,params:A,matched:$,meta:nu($)}}e.forEach(d=>o(d));function a(){n.length=0,s.clear()}return{addRoute:o,resolve:h,removeRoute:i,clearRoutes:a,getRoutes:l,getRecordMatcher:r}}function pr(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function gr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:tu(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function tu(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function mr(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function nu(e){return e.reduce((t,n)=>K(t,n.meta),{})}function vr(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function su(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;Wo(e,t[o])<0?s=o:n=o+1}const r=ru(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function ru(e){let t=e;for(;t=t.parent;)if(qo(t)&&Wo(e,t)===0)return t}function qo({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function ou(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const o=s[r].replace(Fo," "),i=o.indexOf("="),l=nn(i<0?o:o.slice(0,i)),c=i<0?null:nn(o.slice(i+1));if(l in t){let h=t[l];Oe(h)||(h=t[l]=[h]),h.push(c)}else t[l]=c}return t}function _r(e){let t="";for(let n in e){const s=e[n];if(n=Rc(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(Oe(s)?s.map(o=>o&&ss(o)):[s&&ss(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function iu(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=Oe(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const lu=Symbol(""),yr=Symbol(""),As=Symbol(""),Go=Symbol(""),os=Symbol("");function Ft(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function it(e,t,n,s,r,o=i=>i()){const i=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((l,c)=>{const h=g=>{g===!1?c(It(4,{from:n,to:t})):g instanceof Error?c(g):Kc(g)?c(It(2,{from:t,to:g})):(i&&s.enterCallbacks[r]===i&&typeof g=="function"&&i.push(g),l())},a=o(()=>e.call(s&&s.instances[r],t,n,h));let d=Promise.resolve(a);e.length<3&&(d=d.then(h)),d.catch(g=>c(g))})}function Un(e,t,n,s,r=o=>o()){const o=[];for(const i of e)for(const l in i.components){let c=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(Lo(c)){const a=(c.__vccOpts||c)[t];a&&o.push(it(a,n,s,i,l,r))}else{let h=c();o.push(()=>h.then(a=>{if(!a)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const d=pc(a)?a.default:a;i.mods[l]=a,i.components[l]=d;const m=(d.__vccOpts||d)[t];return m&&it(m,n,s,i,l,r)()}))}}return o}function br(e){const t=Ye(As),n=Ye(Go),s=Ce(()=>{const c=Ge(e.to);return t.resolve(c)}),r=Ce(()=>{const{matched:c}=s.value,{length:h}=c,a=c[h-1],d=n.matched;if(!a||!d.length)return-1;const g=d.findIndex(Tt.bind(null,a));if(g>-1)return g;const m=wr(c[h-2]);return h>1&&wr(a)===m&&d[d.length-1].path!==m?d.findIndex(Tt.bind(null,c[h-2])):g}),o=Ce(()=>r.value>-1&&au(n.params,s.value.params)),i=Ce(()=>r.value>-1&&r.value===n.matched.length-1&&ko(n.params,s.value.params));function l(c={}){if(fu(c)){const h=t[Ge(e.replace)?"replace":"push"](Ge(e.to)).catch(Gt);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>h),h}return Promise.resolve()}return{route:s,href:Ce(()=>s.value.href),isActive:o,isExactActive:i,navigate:l}}function cu(e){return e.length===1?e[0]:e}const uu=zt({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:br,setup(e,{slots:t}){const n=Sn(br(e)),{options:s}=Ye(As),r=Ce(()=>({[xr(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[xr(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&cu(t.default(n));return e.custom?o:Ho("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),is=uu;function fu(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function au(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!Oe(r)||r.length!==s.length||s.some((o,i)=>o!==r[i]))return!1}return!0}function wr(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const xr=(e,t,n)=>e??t??n,du=zt({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=Ye(os),r=Ce(()=>e.route||s.value),o=Ye(yr,0),i=Ce(()=>{let h=Ge(o);const{matched:a}=r.value;let d;for(;(d=a[h])&&!d.components;)h++;return h}),l=Ce(()=>r.value.matched[i.value]);an(yr,Ce(()=>i.value+1)),an(lu,l),an(os,r);const c=Xr();return dn(()=>[c.value,l.value,e.name],([h,a,d],[g,m,A])=>{a&&(a.instances[d]=h,m&&m!==a&&h&&h===g&&(a.leaveGuards.size||(a.leaveGuards=m.leaveGuards),a.updateGuards.size||(a.updateGuards=m.updateGuards))),h&&a&&(!m||!Tt(a,m)||!g)&&(a.enterCallbacks[d]||[]).forEach(O=>O(h))},{flush:"post"}),()=>{const h=r.value,a=e.name,d=l.value,g=d&&d.components[a];if(!g)return Er(n.default,{Component:g,route:h});const m=d.props[a],A=m?m===!0?h.params:typeof m=="function"?m(h):m:null,V=Ho(g,K({},A,t,{onVnodeUnmounted:$=>{$.component.isUnmounted&&(d.instances[a]=null)},ref:c}));return Er(n.default,{Component:V,route:h})||V}}});function Er(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Yo=du;function hu(e){const t=eu(e.routes,e),n=e.parseQuery||ou,s=e.stringifyQuery||_r,r=e.history,o=Ft(),i=Ft(),l=Ft(),c=Ai(st);let h=st;St&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const a=kn.bind(null,_=>""+_),d=kn.bind(null,Pc),g=kn.bind(null,nn);function m(_,P){let R,T;return Uo(_)?(R=t.getRecordMatcher(_),T=P):T=_,t.addRoute(T,R)}function A(_){const P=t.getRecordMatcher(_);P&&t.removeRoute(P)}function O(){return t.getRoutes().map(_=>_.record)}function V(_){return!!t.getRecordMatcher(_)}function $(_,P){if(P=K({},P||c.value),typeof _=="string"){const p=Bn(n,_,P.path),v=t.resolve({path:p.path},P),b=r.createHref(p.fullPath);return K(p,v,{params:g(v.params),hash:nn(p.hash),redirectedFrom:void 0,href:b})}let R;if(_.path!=null)R=K({},_,{path:Bn(n,_.path,P.path).path});else{const p=K({},_.params);for(const v in p)p[v]==null&&delete p[v];R=K({},_,{params:d(p)}),P.params=d(P.params)}const T=t.resolve(R,P),X=_.hash||"";T.params=a(g(T.params));const u=Mc(s,K({},_,{hash:Sc(X),path:T.path})),f=r.createHref(u);return K({fullPath:u,hash:X,query:s===_r?iu(_.query):_.query||{}},T,{redirectedFrom:void 0,href:f})}function I(_){return typeof _=="string"?Bn(n,_,c.value.path):K({},_)}function L(_,P){if(h!==_)return It(8,{from:P,to:_})}function M(_){return ne(_)}function J(_){return M(K(I(_),{replace:!0}))}function le(_){const P=_.matched[_.matched.length-1];if(P&&P.redirect){const{redirect:R}=P;let T=typeof R=="function"?R(_):R;return typeof T=="string"&&(T=T.includes("?")||T.includes("#")?T=I(T):{path:T},T.params={}),K({query:_.query,hash:_.hash,params:T.path!=null?{}:_.params},T)}}function ne(_,P){const R=h=$(_),T=c.value,X=_.state,u=_.force,f=_.replace===!0,p=le(R);if(p)return ne(K(I(p),{state:typeof p=="object"?K({},X,p.state):X,force:u,replace:f}),P||R);const v=R;v.redirectedFrom=P;let b;return!u&&Tc(s,T,R)&&(b=It(16,{to:v,from:T}),ze(T,T,!0,!1)),(b?Promise.resolve(b):Te(v,T)).catch(y=>Ke(y)?Ke(y,2)?y:nt(y):U(y,v,T)).then(y=>{if(y){if(Ke(y,2))return ne(K({replace:f},I(y.to),{state:typeof y.to=="object"?K({},X,y.to.state):X,force:u}),P||v)}else y=at(v,T,!0,f,X);return tt(v,T,y),y})}function Me(_,P){const R=L(_,P);return R?Promise.reject(R):Promise.resolve()}function et(_){const P=wt.values().next().value;return P&&typeof P.runWithContext=="function"?P.runWithContext(_):_()}function Te(_,P){let R;const[T,X,u]=pu(_,P);R=Un(T.reverse(),"beforeRouteLeave",_,P);for(const p of T)p.leaveGuards.forEach(v=>{R.push(it(v,_,P))});const f=Me.bind(null,_,P);return R.push(f),Se(R).then(()=>{R=[];for(const p of o.list())R.push(it(p,_,P));return R.push(f),Se(R)}).then(()=>{R=Un(X,"beforeRouteUpdate",_,P);for(const p of X)p.updateGuards.forEach(v=>{R.push(it(v,_,P))});return R.push(f),Se(R)}).then(()=>{R=[];for(const p of u)if(p.beforeEnter)if(Oe(p.beforeEnter))for(const v of p.beforeEnter)R.push(it(v,_,P));else R.push(it(p.beforeEnter,_,P));return R.push(f),Se(R)}).then(()=>(_.matched.forEach(p=>p.enterCallbacks={}),R=Un(u,"beforeRouteEnter",_,P,et),R.push(f),Se(R))).then(()=>{R=[];for(const p of i.list())R.push(it(p,_,P));return R.push(f),Se(R)}).catch(p=>Ke(p,8)?p:Promise.reject(p))}function tt(_,P,R){l.list().forEach(T=>et(()=>T(_,P,R)))}function at(_,P,R,T,X){const u=L(_,P);if(u)return u;const f=P===st,p=St?history.state:{};R&&(T||f?r.replace(_.fullPath,K({scroll:f&&p&&p.scroll},X)):r.push(_.fullPath,X)),c.value=_,ze(_,P,R,f),nt()}let Ie;function Ht(){Ie||(Ie=r.listen((_,P,R)=>{if(!ln.listening)return;const T=$(_),X=le(T);if(X){ne(K(X,{replace:!0,force:!0}),T).catch(Gt);return}h=T;const u=c.value;St&&Nc(ur(u.fullPath,R.delta),On()),Te(T,u).catch(f=>Ke(f,12)?f:Ke(f,2)?(ne(K(I(f.to),{force:!0}),T).then(p=>{Ke(p,20)&&!R.delta&&R.type===sn.pop&&r.go(-1,!1)}).catch(Gt),Promise.reject()):(R.delta&&r.go(-R.delta,!1),U(f,T,u))).then(f=>{f=f||at(T,u,!1),f&&(R.delta&&!Ke(f,8)?r.go(-R.delta,!1):R.type===sn.pop&&Ke(f,20)&&r.go(-1,!1)),tt(T,u,f)}).catch(Gt)}))}let yt=Ft(),oe=Ft(),Y;function U(_,P,R){nt(_);const T=oe.list();return T.length?T.forEach(X=>X(_,P,R)):console.error(_),Promise.reject(_)}function Be(){return Y&&c.value!==st?Promise.resolve():new Promise((_,P)=>{yt.add([_,P])})}function nt(_){return Y||(Y=!_,Ht(),yt.list().forEach(([P,R])=>_?R(_):P()),yt.reset()),_}function ze(_,P,R,T){const{scrollBehavior:X}=e;if(!St||!X)return Promise.resolve();const u=!R&&Vc(ur(_.fullPath,0))||(T||!R)&&history.state&&history.state.scroll||null;return no().then(()=>X(_,P,u)).then(f=>f&&Fc(f)).catch(f=>U(f,_,P))}const ge=_=>r.go(_);let bt;const wt=new Set,ln={currentRoute:c,listening:!0,addRoute:m,removeRoute:A,clearRoutes:t.clearRoutes,hasRoute:V,getRoutes:O,resolve:$,options:e,push:M,replace:J,go:ge,back:()=>ge(-1),forward:()=>ge(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:oe.add,isReady:Be,install(_){const P=this;_.component("RouterLink",is),_.component("RouterView",Yo),_.config.globalProperties.$router=P,Object.defineProperty(_.config.globalProperties,"$route",{enumerable:!0,get:()=>Ge(c)}),St&&!bt&&c.value===st&&(bt=!0,M(r.location).catch(X=>{}));const R={};for(const X in st)Object.defineProperty(R,X,{get:()=>c.value[X],enumerable:!0});_.provide(As,P),_.provide(Go,Yr(R)),_.provide(os,c);const T=_.unmount;wt.add(_),_.unmount=function(){wt.delete(_),wt.size<1&&(h=st,Ie&&Ie(),Ie=null,c.value=st,bt=!1,Y=!1),T()}}};function Se(_){return _.reduce((P,R)=>P.then(()=>et(R)),Promise.resolve())}return ln}function pu(e,t){const n=[],s=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(h=>Tt(h,l))?s.push(l):n.push(l));const c=e.matched[i];c&&(t.matched.find(h=>Tt(h,c))||r.push(c))}return[n,s,r]}const gu={class:"greetings"},mu={class:"green"},vu=zt({__name:"HelloWorld",props:{msg:{}},setup(e){return(t,n)=>(Re(),ke("div",gu,[D("h1",mu,Ir(t.msg),1),n[0]||(n[0]=D("h3",null,[k(" You’ve successfully created a project with "),D("a",{href:"https://vite.dev/",target:"_blank",rel:"noopener"},"Vite"),k(" + "),D("a",{href:"https://vuejs.org/",target:"_blank",rel:"noopener"},"Vue 3"),k(". What's next? ")],-1))]))}}),ft=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},_u=ft(vu,[["__scopeId","data-v-d1bb330e"]]),yu={class:"wrapper"},bu=zt({__name:"App",setup(e){return(t,n)=>(Re(),ke(ye,null,[D("header",null,[n[2]||(n[2]=D("img",{alt:"Vue logo",class:"logo",src:hc,width:"125",height:"125"},null,-1)),D("div",yu,[Q(_u,{msg:"You did it!"}),D("nav",null,[Q(Ge(is),{to:"/"},{default:ie(()=>n[0]||(n[0]=[k("Home")])),_:1,__:[0]}),Q(Ge(is),{to:"/about"},{default:ie(()=>n[1]||(n[1]=[k("About")])),_:1,__:[1]})])])]),Q(Ge(Yo))],64))}}),wu=ft(bu,[["__scopeId","data-v-85852c48"]]),xu="modulepreload",Eu=function(e){return"/"+e},Sr={},Su=function(t,n,s){let r=Promise.resolve();if(n&&n.length>0){let c=function(h){return Promise.all(h.map(a=>Promise.resolve(a).then(d=>({status:"fulfilled",value:d}),d=>({status:"rejected",reason:d}))))};document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),l=i?.nonce||i?.getAttribute("nonce");r=c(n.map(h=>{if(h=Eu(h),h in Sr)return;Sr[h]=!0;const a=h.endsWith(".css"),d=a?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${h}"]${d}`))return;const g=document.createElement("link");if(g.rel=a?"stylesheet":xu,a||(g.as="script"),g.crossOrigin="",g.href=h,l&&g.setAttribute("nonce",l),document.head.appendChild(g),a)return new Promise((m,A)=>{g.addEventListener("load",m),g.addEventListener("error",()=>A(new Error(`Unable to preload CSS for ${h}`)))})}))}function o(i){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i}return r.then(i=>{for(const l of i||[])l.status==="rejected"&&o(l.reason);return t().catch(o)})},Ru={},Cu={class:"item"},Pu={class:"details"};function Au(e,t){return Re(),ke("div",Cu,[D("i",null,[Ln(e.$slots,"icon",{},void 0)]),D("div",Pu,[D("h3",null,[Ln(e.$slots,"heading",{},void 0)]),Ln(e.$slots,"default",{},void 0)])])}const Nt=ft(Ru,[["render",Au],["__scopeId","data-v-fd0742eb"]]),Ou={},Mu={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"17",fill:"currentColor"};function Tu(e,t){return Re(),ke("svg",Mu,t[0]||(t[0]=[D("path",{d:"M11 2.253a1 1 0 1 0-2 0h2zm-2 13a1 1 0 1 0 2 0H9zm.447-12.167a1 1 0 1 0 1.107-1.666L9.447 3.086zM1 2.253L.447 1.42A1 1 0 0 0 0 2.253h1zm0 13H0a1 1 0 0 0 1.553.833L1 15.253zm8.447.833a1 1 0 1 0 1.107-1.666l-1.107 1.666zm0-14.666a1 1 0 1 0 1.107 1.666L9.447 1.42zM19 2.253h1a1 1 0 0 0-.447-.833L19 2.253zm0 13l-.553.833A1 1 0 0 0 20 15.253h-1zm-9.553-.833a1 1 0 1 0 1.107 1.666L9.447 14.42zM9 2.253v13h2v-13H9zm1.553-.833C9.203.523 7.42 0 5.5 0v2c1.572 0 2.961.431 3.947 1.086l1.107-1.666zM5.5 0C3.58 0 1.797.523.447 1.42l1.107 1.666C2.539 2.431 3.928 2 5.5 2V0zM0 2.253v13h2v-13H0zm1.553 13.833C2.539 15.431 3.928 15 5.5 15v-2c-1.92 0-3.703.523-5.053 1.42l1.107 1.666zM5.5 15c1.572 0 2.961.431 3.947 1.086l1.107-1.666C9.203 13.523 7.42 13 5.5 13v2zm5.053-11.914C11.539 2.431 12.928 2 14.5 2V0c-1.92 0-3.703.523-5.053 1.42l1.107 1.666zM14.5 2c1.573 0 2.961.431 3.947 1.086l1.107-1.666C18.203.523 16.421 0 14.5 0v2zm3.5.253v13h2v-13h-2zm1.553 12.167C18.203 13.523 16.421 13 14.5 13v2c1.573 0 2.961.431 3.947 1.086l1.107-1.666zM14.5 13c-1.92 0-3.703.523-5.053 1.42l1.107 1.666C11.539 15.431 12.928 15 14.5 15v-2z"},null,-1)]))}const Iu=ft(Ou,[["render",Tu]]),zu={},Hu={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink","aria-hidden":"true",role:"img",class:"iconify iconify--mdi",width:"24",height:"24",preserveAspectRatio:"xMidYMid meet",viewBox:"0 0 24 24"};function $u(e,t){return Re(),ke("svg",Hu,t[0]||(t[0]=[D("path",{d:"M20 18v-4h-3v1h-2v-1H9v1H7v-1H4v4h16M6.33 8l-1.74 4H7v-1h2v1h6v-1h2v1h2.41l-1.74-4H6.33M9 5v1h6V5H9m12.84 7.61c.1.22.16.48.16.8V18c0 .53-.21 1-.6 1.41c-.4.4-.85.59-1.4.59H4c-.55 0-1-.19-1.4-.59C2.21 19 2 18.53 2 18v-4.59c0-.32.06-.58.16-.8L4.5 7.22C4.84 6.41 5.45 6 6.33 6H7V5c0-.55.18-1 .57-1.41C7.96 3.2 8.44 3 9 3h6c.56 0 1.04.2 1.43.59c.39.41.57.86.57 1.41v1h.67c.88 0 1.49.41 1.83 1.22l2.34 5.39z",fill:"currentColor"},null,-1)]))}const Lu=ft(zu,[["render",$u]]),ju={},Fu={xmlns:"http://www.w3.org/2000/svg",width:"18",height:"20",fill:"currentColor"};function Nu(e,t){return Re(),ke("svg",Fu,t[0]||(t[0]=[D("path",{d:"M11.447 8.894a1 1 0 1 0-.894-1.789l.894 1.789zm-2.894-.789a1 1 0 1 0 .894 1.789l-.894-1.789zm0 1.789a1 1 0 1 0 .894-1.789l-.894 1.789zM7.447 7.106a1 1 0 1 0-.894 1.789l.894-1.789zM10 9a1 1 0 1 0-2 0h2zm-2 2.5a1 1 0 1 0 2 0H8zm9.447-5.606a1 1 0 1 0-.894-1.789l.894 1.789zm-2.894-.789a1 1 0 1 0 .894 1.789l-.894-1.789zm2 .789a1 1 0 1 0 .894-1.789l-.894 1.789zm-1.106-2.789a1 1 0 1 0-.894 1.789l.894-1.789zM18 5a1 1 0 1 0-2 0h2zm-2 2.5a1 1 0 1 0 2 0h-2zm-5.447-4.606a1 1 0 1 0 .894-1.789l-.894 1.789zM9 1l.447-.894a1 1 0 0 0-.894 0L9 1zm-2.447.106a1 1 0 1 0 .894 1.789l-.894-1.789zm-6 3a1 1 0 1 0 .894 1.789L.553 4.106zm2.894.789a1 1 0 1 0-.894-1.789l.894 1.789zm-2-.789a1 1 0 1 0-.894 1.789l.894-1.789zm1.106 2.789a1 1 0 1 0 .894-1.789l-.894 1.789zM2 5a1 1 0 1 0-2 0h2zM0 7.5a1 1 0 1 0 2 0H0zm8.553 12.394a1 1 0 1 0 .894-1.789l-.894 1.789zm-1.106-2.789a1 1 0 1 0-.894 1.789l.894-1.789zm1.106 1a1 1 0 1 0 .894 1.789l-.894-1.789zm2.894.789a1 1 0 1 0-.894-1.789l.894 1.789zM8 19a1 1 0 1 0 2 0H8zm2-2.5a1 1 0 1 0-2 0h2zm-7.447.394a1 1 0 1 0 .894-1.789l-.894 1.789zM1 15H0a1 1 0 0 0 .553.894L1 15zm1-2.5a1 1 0 1 0-2 0h2zm12.553 2.606a1 1 0 1 0 .894 1.789l-.894-1.789zM17 15l.447.894A1 1 0 0 0 18 15h-1zm1-2.5a1 1 0 1 0-2 0h2zm-7.447-5.394l-2 1 .894 1.789 2-1-.894-1.789zm-1.106 1l-2-1-.894 1.789 2 1 .894-1.789zM8 9v2.5h2V9H8zm8.553-4.894l-2 1 .894 1.789 2-1-.894-1.789zm.894 0l-2-1-.894 1.789 2 1 .894-1.789zM16 5v2.5h2V5h-2zm-4.553-3.894l-2-1-.894 1.789 2 1 .894-1.789zm-2.894-1l-2 1 .894 1.789 2-1L8.553.106zM1.447 5.894l2-1-.894-1.789-2 1 .894 1.789zm-.894 0l2 1 .894-1.789-2-1-.894 1.789zM0 5v2.5h2V5H0zm9.447 13.106l-2-1-.894 1.789 2 1 .894-1.789zm0 1.789l2-1-.894-1.789-2 1 .894 1.789zM10 19v-2.5H8V19h2zm-6.553-3.894l-2-1-.894 1.789 2 1 .894-1.789zM2 15v-2.5H0V15h2zm13.447 1.894l2-1-.894-1.789-2 1 .894 1.789zM18 15v-2.5h-2V15h2z"},null,-1)]))}const Vu=ft(ju,[["render",Nu]]),Du={},ku={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"currentColor"};function Bu(e,t){return Re(),ke("svg",ku,t[0]||(t[0]=[D("path",{d:"M15 4a1 1 0 1 0 0 2V4zm0 11v-1a1 1 0 0 0-1 1h1zm0 4l-.707.707A1 1 0 0 0 16 19h-1zm-4-4l.707-.707A1 1 0 0 0 11 14v1zm-4.707-1.293a1 1 0 0 0-1.414 1.414l1.414-1.414zm-.707.707l-.707-.707.707.707zM9 11v-1a1 1 0 0 0-.707.293L9 11zm-4 0h1a1 1 0 0 0-1-1v1zm0 4H4a1 1 0 0 0 1.707.707L5 15zm10-9h2V4h-2v2zm2 0a1 1 0 0 1 1 1h2a3 3 0 0 0-3-3v2zm1 1v6h2V7h-2zm0 6a1 1 0 0 1-1 1v2a3 3 0 0 0 3-3h-2zm-1 1h-2v2h2v-2zm-3 1v4h2v-4h-2zm1.707 3.293l-4-4-1.414 1.414 4 4 1.414-1.414zM11 14H7v2h4v-2zm-4 0c-.276 0-.525-.111-.707-.293l-1.414 1.414C5.42 15.663 6.172 16 7 16v-2zm-.707 1.121l3.414-3.414-1.414-1.414-3.414 3.414 1.414 1.414zM9 12h4v-2H9v2zm4 0a3 3 0 0 0 3-3h-2a1 1 0 0 1-1 1v2zm3-3V3h-2v6h2zm0-6a3 3 0 0 0-3-3v2a1 1 0 0 1 1 1h2zm-3-3H3v2h10V0zM3 0a3 3 0 0 0-3 3h2a1 1 0 0 1 1-1V0zM0 3v6h2V3H0zm0 6a3 3 0 0 0 3 3v-2a1 1 0 0 1-1-1H0zm3 3h2v-2H3v2zm1-1v4h2v-4H4zm1.707 4.707l.586-.586-1.414-1.414-.586.586 1.414 1.414z"},null,-1)]))}const Uu=ft(Du,[["render",Bu]]),Ku={},Wu={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"currentColor"};function qu(e,t){return Re(),ke("svg",Wu,t[0]||(t[0]=[D("path",{d:"M10 3.22l-.61-.6a5.5 5.5 0 0 0-7.666.105 5.5 5.5 0 0 0-.114 7.665L10 18.78l8.39-8.4a5.5 5.5 0 0 0-.114-7.665 5.5 5.5 0 0 0-7.666-.105l-.61.61z"},null,-1)]))}const Gu=ft(Ku,[["render",qu]]),Yu=zt({__name:"TheWelcome",setup(e){const t=()=>fetch("/__open-in-editor?file=README.md");return(n,s)=>(Re(),ke(ye,null,[Q(Nt,null,{icon:ie(()=>[Q(Iu)]),heading:ie(()=>s[0]||(s[0]=[k("Documentation")])),default:ie(()=>[s[1]||(s[1]=k(" Vue’s ")),s[2]||(s[2]=D("a",{href:"https://vuejs.org/",target:"_blank",rel:"noopener"},"official documentation",-1)),s[3]||(s[3]=k(" provides you with all information you need to get started. "))]),_:1,__:[1,2,3]}),Q(Nt,null,{icon:ie(()=>[Q(Lu)]),heading:ie(()=>s[4]||(s[4]=[k("Tooling")])),default:ie(()=>[s[6]||(s[6]=k(" This project is served and bundled with ")),s[7]||(s[7]=D("a",{href:"https://vite.dev/guide/features.html",target:"_blank",rel:"noopener"},"Vite",-1)),s[8]||(s[8]=k(". The recommended IDE setup is ")),s[9]||(s[9]=D("a",{href:"https://code.visualstudio.com/",target:"_blank",rel:"noopener"},"VSCode",-1)),s[10]||(s[10]=k(" + ")),s[11]||(s[11]=D("a",{href:"https://github.com/vuejs/language-tools",target:"_blank",rel:"noopener"},"Vue - Official",-1)),s[12]||(s[12]=k(". If you need to test your components and web pages, check out ")),s[13]||(s[13]=D("a",{href:"https://vitest.dev/",target:"_blank",rel:"noopener"},"Vitest",-1)),s[14]||(s[14]=k(" and ")),s[15]||(s[15]=D("a",{href:"https://www.cypress.io/",target:"_blank",rel:"noopener"},"Cypress",-1)),s[16]||(s[16]=k(" / ")),s[17]||(s[17]=D("a",{href:"https://playwright.dev/",target:"_blank",rel:"noopener"},"Playwright",-1)),s[18]||(s[18]=k(". ")),s[19]||(s[19]=D("br",null,null,-1)),s[20]||(s[20]=k(" More instructions are available in ")),D("a",{href:"javascript:void(0)",onClick:t},s[5]||(s[5]=[D("code",null,"README.md",-1)])),s[21]||(s[21]=k(". "))]),_:1,__:[6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21]}),Q(Nt,null,{icon:ie(()=>[Q(Vu)]),heading:ie(()=>s[22]||(s[22]=[k("Ecosystem")])),default:ie(()=>[s[23]||(s[23]=k(" Get official tools and libraries for your project: ")),s[24]||(s[24]=D("a",{href:"https://pinia.vuejs.org/",target:"_blank",rel:"noopener"},"Pinia",-1)),s[25]||(s[25]=k(", ")),s[26]||(s[26]=D("a",{href:"https://router.vuejs.org/",target:"_blank",rel:"noopener"},"Vue Router",-1)),s[27]||(s[27]=k(", ")),s[28]||(s[28]=D("a",{href:"https://test-utils.vuejs.org/",target:"_blank",rel:"noopener"},"Vue Test Utils",-1)),s[29]||(s[29]=k(", and ")),s[30]||(s[30]=D("a",{href:"https://github.com/vuejs/devtools",target:"_blank",rel:"noopener"},"Vue Dev Tools",-1)),s[31]||(s[31]=k(". If you need more resources, we suggest paying ")),s[32]||(s[32]=D("a",{href:"https://github.com/vuejs/awesome-vue",target:"_blank",rel:"noopener"},"Awesome Vue",-1)),s[33]||(s[33]=k(" a visit. "))]),_:1,__:[23,24,25,26,27,28,29,30,31,32,33]}),Q(Nt,null,{icon:ie(()=>[Q(Uu)]),heading:ie(()=>s[34]||(s[34]=[k("Community")])),default:ie(()=>[s[35]||(s[35]=k(" Got stuck? Ask your question on ")),s[36]||(s[36]=D("a",{href:"https://chat.vuejs.org",target:"_blank",rel:"noopener"},"Vue Land",-1)),s[37]||(s[37]=k(" (our official Discord server), or ")),s[38]||(s[38]=D("a",{href:"https://stackoverflow.com/questions/tagged/vue.js",target:"_blank",rel:"noopener"},"StackOverflow",-1)),s[39]||(s[39]=k(". You should also follow the official ")),s[40]||(s[40]=D("a",{href:"https://bsky.app/profile/vuejs.org",target:"_blank",rel:"noopener"},"@vuejs.org",-1)),s[41]||(s[41]=k(" Bluesky account or the ")),s[42]||(s[42]=D("a",{href:"https://x.com/vuejs",target:"_blank",rel:"noopener"},"@vuejs",-1)),s[43]||(s[43]=k(" X account for latest news in the Vue world. "))]),_:1,__:[35,36,37,38,39,40,41,42,43]}),Q(Nt,null,{icon:ie(()=>[Q(Gu)]),heading:ie(()=>s[44]||(s[44]=[k("Support Vue")])),default:ie(()=>[s[45]||(s[45]=k(" As an independent project, Vue relies on community backing for its sustainability. You can help us by ")),s[46]||(s[46]=D("a",{href:"https://vuejs.org/sponsor/",target:"_blank",rel:"noopener"},"becoming a sponsor",-1)),s[47]||(s[47]=k(". "))]),_:1,__:[45,46,47]})],64))}}),Qu=zt({__name:"HomeView",setup(e){return(t,n)=>(Re(),ke("main",null,[Q(Yu)]))}}),Ju=hu({history:Uc("/"),routes:[{path:"/",name:"home",component:Qu},{path:"/about",name:"about",component:()=>Su(()=>import("./AboutView-ltg8wc-7.js"),__vite__mapDeps([0,1]))}]}),Os=cc(wu);Os.use(dc());Os.use(Ju);Os.mount("#app");export{ft as _,D as a,ke as c,Re as o};
