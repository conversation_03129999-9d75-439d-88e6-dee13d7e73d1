/**
 * G6自定义字段节点注册
 * 用于注册卡片式表节点，支持字段列表显示
 * 注意：此文件将在G6图谱初始化任务中完善
 */

import type { LineageNode } from '@/types/lineage'
import { getFieldTypeColor } from './graphDataTransform'

/**
 * 节点样式配置
 */
export interface NodeStyleConfig {
  // 节点基础样式
  backgroundColor: string;
  borderColor: string;
  borderWidth: number;
  borderRadius: number;
  shadowColor: string;
  shadowBlur: number;
  shadowOffsetX: number;
  shadowOffsetY: number;
  
  // 标题样式
  headerBackgroundColor: string;
  headerTextColor: string;
  headerFontSize: number;
  headerFontWeight: string;
  headerPadding: number;
  
  // 字段样式
  fieldTextColor: string;
  fieldFontSize: number;
  fieldLineHeight: number;
  fieldPadding: number;
  fieldHoverBackgroundColor: string;
  fieldActiveBackgroundColor: string;
  
  // 类型标签样式
  typeTagBackgroundColor: string;
  typeTagTextColor: string;
  typeTagFontSize: number;
  typeTagPadding: number;
  typeTagBorderRadius: number;
}

/**
 * 默认节点样式
 */
export const DEFAULT_NODE_STYLE: NodeStyleConfig = {
  backgroundColor: '#ffffff',
  borderColor: '#d9d9d9',
  borderWidth: 1,
  borderRadius: 6,
  shadowColor: 'rgba(0, 0, 0, 0.1)',
  shadowBlur: 8,
  shadowOffsetX: 0,
  shadowOffsetY: 2,
  
  headerBackgroundColor: '#fafafa',
  headerTextColor: '#262626',
  headerFontSize: 14,
  headerFontWeight: 'bold',
  headerPadding: 12,
  
  fieldTextColor: '#595959',
  fieldFontSize: 12,
  fieldLineHeight: 20,
  fieldPadding: 8,
  fieldHoverBackgroundColor: '#f0f0f0',
  fieldActiveBackgroundColor: '#e6f7ff',
  
  typeTagBackgroundColor: '#f0f0f0',
  typeTagTextColor: '#666666',
  typeTagFontSize: 10,
  typeTagPadding: 2,
  typeTagBorderRadius: 2
}

/**
 * 注册表节点类型
 * @param styleCfg 样式配置
 * 注意：此函数将在G6图谱初始化任务中实现
 */
export function registerTableNode(styleCfg: Partial<NodeStyleConfig> = {}) {
  const style = { ...DEFAULT_NODE_STYLE, ...styleCfg }

  // TODO: 在G6图谱初始化任务中实现节点注册
  console.log('registerTableNode called with style:', style)

  // TODO: 在G6图谱初始化任务中实现完整的节点注册逻辑
}

/**
 * 计算节点高度
 * @param fields 字段列表
 * @param style 样式配置
 * @returns 节点高度
 */
function calculateNodeHeight(fields: LineageNode[], style: NodeStyleConfig): number {
  const headerHeight = style.headerFontSize + style.headerPadding * 2
  const fieldsHeight = fields.length * style.fieldLineHeight + style.fieldPadding * 2
  return headerHeight + fieldsHeight + 20 // 额外的底部间距
}

/**
 * 格式化数据类型显示
 * @param dataType 数据类型对象
 * @returns 格式化后的类型字符串
 */
function formatDataType(dataType: any): string {
  let result = dataType.type
  
  if (dataType.length) {
    result += `(${dataType.length})`
  } else if (dataType.precision && dataType.scale) {
    result += `(${dataType.precision},${dataType.scale})`
  } else if (dataType.precision) {
    result += `(${dataType.precision})`
  }
  
  return result
}
