export interface SummaryProps {
    fixed?: boolean | 'top' | 'bottom';
}
declare const Summary: import("vue").DefineComponent<SummaryProps, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<SummaryProps> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default Summary;
