{"name": "@codemirror/lang-sql", "version": "6.9.0", "description": "SQL language support for the CodeMirror code editor", "scripts": {"test": "cm-runtests", "prepare": "cm-buildhelper src/sql.ts"}, "keywords": ["editor", "code"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://marijnhaverbeke.nl"}, "type": "module", "main": "dist/index.cjs", "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "types": "dist/index.d.ts", "module": "dist/index.js", "sideEffects": false, "license": "MIT", "dependencies": {"@codemirror/autocomplete": "^6.0.0", "@lezer/common": "^1.2.0", "@lezer/highlight": "^1.0.0", "@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@lezer/lr": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^1.0.0"}, "repository": {"type": "git", "url": "https://github.com/codemirror/lang-sql.git"}}