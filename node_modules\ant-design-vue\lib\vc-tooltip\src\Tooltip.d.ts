import type { CSSProperties, PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    trigger: import("vue-types").VueTypeValidableDef<any> & {
        default: any;
    };
    defaultVisible: {
        type: BooleanConstructor;
        default: any;
    };
    visible: {
        type: BooleanConstructor;
        default: any;
    };
    placement: import("vue-types").VueTypeValidableDef<string> & {
        default: string;
    } & {
        default: string;
    };
    transitionName: StringConstructor;
    animation: import("vue-types").VueTypeValidableDef<any>;
    afterVisibleChange: import("vue-types").VueTypeValidableDef<(...args: any[]) => any> & {
        default: (...args: any[]) => any;
    } & {
        default: (...args: any[]) => any;
    };
    overlayStyle: {
        type: PropType<CSSProperties>;
        default: CSSProperties;
    };
    overlayClassName: StringConstructor;
    prefixCls: import("vue-types").VueTypeValidableDef<string> & {
        default: string;
    } & {
        default: string;
    };
    mouseEnterDelay: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    mouseLeaveDelay: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    getPopupContainer: PropType<(triggerNode?: HTMLElement) => HTMLElement>;
    destroyTooltipOnHide: {
        type: BooleanConstructor;
        default: boolean;
    };
    align: import("vue-types").VueTypeValidableDef<{
        [key: string]: any;
    }> & {
        default: () => {
            [key: string]: any;
        };
    } & {
        default: () => {
            [key: string]: any;
        };
    };
    arrowContent: import("vue-types").VueTypeValidableDef<any> & {
        default: any;
    };
    tipId: StringConstructor;
    builtinPlacements: import("vue-types").VueTypeValidableDef<{
        [key: string]: any;
    }> & {
        default: () => {
            [key: string]: any;
        };
    };
    overlayInnerStyle: {
        type: PropType<CSSProperties>;
        default: CSSProperties;
    };
    popupVisible: {
        type: BooleanConstructor;
        default: any;
    };
    onVisibleChange: FunctionConstructor;
    onPopupAlign: FunctionConstructor;
    arrow: {
        type: BooleanConstructor;
        default: boolean;
    };
}>, () => import("vue/jsx-runtime").JSX.Element, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    trigger: import("vue-types").VueTypeValidableDef<any> & {
        default: any;
    };
    defaultVisible: {
        type: BooleanConstructor;
        default: any;
    };
    visible: {
        type: BooleanConstructor;
        default: any;
    };
    placement: import("vue-types").VueTypeValidableDef<string> & {
        default: string;
    } & {
        default: string;
    };
    transitionName: StringConstructor;
    animation: import("vue-types").VueTypeValidableDef<any>;
    afterVisibleChange: import("vue-types").VueTypeValidableDef<(...args: any[]) => any> & {
        default: (...args: any[]) => any;
    } & {
        default: (...args: any[]) => any;
    };
    overlayStyle: {
        type: PropType<CSSProperties>;
        default: CSSProperties;
    };
    overlayClassName: StringConstructor;
    prefixCls: import("vue-types").VueTypeValidableDef<string> & {
        default: string;
    } & {
        default: string;
    };
    mouseEnterDelay: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    mouseLeaveDelay: import("vue-types").VueTypeValidableDef<number> & {
        default: number;
    } & {
        default: number;
    };
    getPopupContainer: PropType<(triggerNode?: HTMLElement) => HTMLElement>;
    destroyTooltipOnHide: {
        type: BooleanConstructor;
        default: boolean;
    };
    align: import("vue-types").VueTypeValidableDef<{
        [key: string]: any;
    }> & {
        default: () => {
            [key: string]: any;
        };
    } & {
        default: () => {
            [key: string]: any;
        };
    };
    arrowContent: import("vue-types").VueTypeValidableDef<any> & {
        default: any;
    };
    tipId: StringConstructor;
    builtinPlacements: import("vue-types").VueTypeValidableDef<{
        [key: string]: any;
    }> & {
        default: () => {
            [key: string]: any;
        };
    };
    overlayInnerStyle: {
        type: PropType<CSSProperties>;
        default: CSSProperties;
    };
    popupVisible: {
        type: BooleanConstructor;
        default: any;
    };
    onVisibleChange: FunctionConstructor;
    onPopupAlign: FunctionConstructor;
    arrow: {
        type: BooleanConstructor;
        default: boolean;
    };
}>> & Readonly<{}>, {
    visible: boolean;
    trigger: any;
    prefixCls: string;
    align: {
        [key: string]: any;
    };
    arrow: boolean;
    builtinPlacements: {
        [key: string]: any;
    };
    mouseEnterDelay: number;
    mouseLeaveDelay: number;
    popupVisible: boolean;
    overlayInnerStyle: CSSProperties;
    defaultVisible: boolean;
    placement: string;
    afterVisibleChange: (...args: any[]) => any;
    overlayStyle: CSSProperties;
    destroyTooltipOnHide: boolean;
    arrowContent: any;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
